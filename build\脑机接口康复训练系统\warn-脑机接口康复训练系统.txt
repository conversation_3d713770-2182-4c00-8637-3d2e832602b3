
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), backports.tarfile (optional), distutils.archive_util (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), backports.tarfile (optional), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named ctypes._FuncPointer - imported by ctypes (conditional), comtypes._vtbl (conditional)
missing module named ctypes._CDataType - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional)
missing module named ctypes._CArgObject - imported by ctypes (conditional), comtypes._memberspec (conditional), comtypes.automation (conditional), comtypes._comobject (conditional), comtypes.messageloop (conditional), comtypes.connectionpoints (conditional)
missing module named ctypes._CData - imported by ctypes (conditional), comtypes (conditional)
missing module named pyimod02_importers - imported by G:\ProgramData\anaconda3\envs\nk_pip_clean\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), G:\ProgramData\anaconda3\envs\nk_pip_clean\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed), joblib.externals.loky.backend.fork_exec (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib.parallel (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), tqdm.utils (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), jaraco.collections (conditional), setuptools._distutils.dist (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional), bleak.backends.p4android.client (top-level), bleak.backends.p4android.defs (top-level), bleak.backends.p4android.utils (top-level), bleak.backends.p4android.scanner (top-level)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional), joblib.externals.loky.backend.context (delayed, optional), joblib.externals.loky.backend.utils (optional), joblib.externals.loky.process_executor (optional), mne.utils.config (delayed, optional)
missing module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), sklearn.utils._testing (optional), mne.utils._testing (delayed), mne.io.array.tests.test_array (top-level), mne.io.tests.test_raw (top-level), mne.io.artemis123.tests.test_artemis123 (top-level), mne.io.boxy.tests.test_boxy (top-level), mne.io.brainvision.tests.test_brainvision (top-level), mne.io.bti.tests.test_bti (top-level), mne.io.cnt.tests.test_cnt (top-level), mne.io.ctf.tests.test_ctf (top-level), mne.io.curry.tests.test_curry (top-level), mne.io.edf.tests.test_edf (top-level), mne.io.edf.tests.test_gdf (top-level), mne.io.eeglab.tests.test_eeglab (top-level), mne.io.egi.tests.test_egi (top-level), mne.io.eyelink.tests.test_eyelink (top-level), mne.io.fieldtrip.tests.test_fieldtrip (top-level), mne.io.fiff.tests.test_raw_fiff (top-level), mne.io.kit.tests.test_coreg (top-level), mne.io.kit.tests.test_kit (top-level), mne.io.nedf.tests.test_nedf (top-level), mne.io.nicolet.tests.test_nicolet (top-level), mne.io.nirx.tests.test_nirx (top-level), mne.io.persyst.tests.test_persyst (top-level), mne.io.snirf.tests.test_snirf (top-level), mne.io.tests.test_apply_function (top-level), mne.io.tests.test_compensator (top-level), pooch (delayed), mne.io.tests.test_constants (top-level), mne.io.tests.test_meas_info (top-level), mne.io.tests.test_pick (top-level), mne.io.tests.test_read_raw (top-level), mne.io.tests.test_reference (top-level), mne.io.tests.test_what (top-level), mne.io.tests.test_write (top-level), mne.channels.tests.test_channels (top-level), mne.channels.tests.test_interpolation (top-level), mne.channels.tests.test_layout (top-level), mne.channels.tests.test_montage (top-level), mne.channels.tests.test_standard_montage (top-level), mne.preprocessing.eyetracking.tests.test_calibration (top-level), mne.preprocessing.eyetracking.tests.test_pupillometry (top-level), mne.preprocessing.tests.test_annotate_amplitude (top-level), mne.preprocessing.tests.test_annotate_nan (top-level), mne.preprocessing.tests.test_artifact_detection (top-level), mne.preprocessing.tests.test_csd (top-level), mne.preprocessing.tests.test_ctps (top-level), mne.preprocessing.tests.test_eeglab_infomax (top-level), mne.preprocessing.tests.test_fine_cal (top-level), mne.preprocessing.tests.test_maxwell (top-level), mne.preprocessing.tests.test_hfc (top-level), mne.preprocessing.tests.test_ica (top-level), mne.preprocessing.tests.test_infomax (top-level), mne.preprocessing.tests.test_interpolate (top-level), mne.preprocessing.tests.test_otp (top-level), mne.preprocessing.tests.test_peak_finder (top-level), mne.preprocessing.tests.test_realign (top-level), mne.preprocessing.tests.test_regress (top-level), mne.preprocessing.tests.test_ssp (top-level), mne.preprocessing.tests.test_stim (top-level), mne.preprocessing.tests.test_xdawn (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg._decomp (top-level), scipy.optimize._optimize (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.stats._stats_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.io._netcdf (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), scipy.optimize._minpack_py (top-level), scipy.io._netcdf (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), pyqtgraph.debug (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pandas.compat.numpy.function (top-level), scipy.io._mmio (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
excluded module named doctest - imported by numpy.testing._private.utils (delayed), pytz (delayed), pandas.io.formats.latex (conditional)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), scipy.optimize._lbfgsb_py (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.special._spfun_stats (top-level), scipy.optimize._dual_annealing (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level), sklearn.decomposition._lda (top-level), sklearn.decomposition._pca (top-level), mne.fixes (delayed)
missing module named scipy.special.lpmv - imported by scipy.special (delayed), mne.preprocessing.maxwell (delayed)
missing module named scipy.special.sph_harm - imported by scipy.special (delayed), mne.transforms (delayed), mne.preprocessing.maxwell (delayed), mne.preprocessing.tests.test_maxwell (top-level)
missing module named scipy.special.logit - imported by scipy.special (top-level), sklearn._loss.link (top-level)
missing module named scipy.special.expit - imported by scipy.special (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.linear_model._base (top-level), sklearn._loss.link (top-level), mne.preprocessing.infomax_ (delayed), mne.preprocessing.ica (delayed), sklearn.ensemble._gb_losses (top-level)
missing module named scipy.special.erf - imported by scipy.special (top-level), sklearn.gaussian_process._gpc (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level), sklearn._loss.loss (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._regression (top-level), sklearn.ensemble._weight_boosting (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level), sklearn.neighbors._kde (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._bsplines (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog (top-level), scipy.stats._multivariate (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.special._basic (top-level), scipy.linalg._decomp (top-level), scipy.optimize._minpack_py (top-level)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.double - imported by numpy (top-level), scipy.optimize._nnls (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level)
missing module named nibabel - imported by mne.morph (delayed, conditional), mne.viz._3d (delayed), mne.preprocessing.ieeg._volume (delayed), mne.transforms (delayed), mne.utils.check (delayed, optional)
missing module named 'nibabel.spatialimages' - imported by mne.viz._3d (delayed), mne.preprocessing.ieeg._volume (delayed), mne.bem (delayed), mne.transforms (delayed)
missing module named 'dipy.align' - imported by mne.morph (delayed, conditional), mne.preprocessing.ieeg._volume (delayed), mne.transforms (delayed)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named matplotlib.axes.Axes - imported by matplotlib.axes (delayed), matplotlib.legend (delayed), matplotlib.projections.geo (top-level), matplotlib.projections.polar (top-level), mpl_toolkits.mplot3d.axes3d (top-level), matplotlib.figure (top-level), matplotlib.pyplot (top-level), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), mpl_toolkits.axes_grid1.axes_size (top-level), mne.viz._mpl_figure (delayed), mne.preprocessing.eyetracking.calibration (delayed, conditional), mne.viz.utils (delayed, conditional), mne.viz.topomap (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named 'nibabel.processing' - imported by mne.viz._3d (delayed)
missing module named 'mne.viz._brain.view' - imported by mne.viz._3d (delayed), mne.report.report (delayed)
missing module named 'nilearn.image' - imported by mne.viz._3d (delayed)
missing module named nilearn - imported by mne.viz._3d (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pyarrow - imported by pandas.core.arrays.masked (delayed), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.arrays.arrow.dtype (conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.methods.describe (delayed, conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas._testing (conditional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.arrow.array (conditional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named numba - imported by pyqtgraph.util.numba_helper (delayed, conditional, optional), pyqtgraph.functions_numba (top-level), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.executor (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), mne.fixes (optional)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional), mne.viz.backends._utils (delayed, conditional, optional), bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.client (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional), mne.utils.check (delayed, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'lxml.etree' - imported by pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named lxml - imported by pandas.io.xml (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named array_api_compat - imported by sklearn.utils._array_api (delayed, conditional, optional)
missing module named numpydoc - imported by sklearn.utils._testing (delayed)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
excluded module named mne.viz._brain - imported by mne.viz._3d (delayed), mne.viz (top-level)
missing module named dipy - imported by mne.morph (delayed)
excluded module named mne.viz.backends.renderer - imported by mne.viz.backends (top-level), mne.viz._dipole (delayed), mne.viz._3d (delayed), mne.viz (top-level), mne.report.report (delayed, conditional)
missing module named vtkmodules - imported by mne.viz.backends._utils (delayed, optional)
missing module named qdarkstyle - imported by mne.viz.backends._utils (delayed, conditional, optional)
missing module named darkdetect - imported by mne.viz.backends._utils (delayed, optional)
missing module named 'qtpy.QtGui' - imported by mne.viz.backends._utils (delayed)
missing module named 'qtpy.QtWidgets' - imported by mne.viz.backends._utils (delayed), mne.viz.utils (delayed, conditional)
missing module named 'qtpy.QtCore' - imported by mne.viz.backends._utils (delayed), mne.viz.utils (delayed, conditional)
missing module named mne_qt_browser - imported by mne.viz._figure (delayed, conditional), mne.viz._scraper (delayed)
missing module named 'vtkmodules.vtkFiltersGeometry' - imported by mne.surface (delayed)
missing module named 'vtkmodules.vtkFiltersGeneral' - imported by mne.surface (delayed)
missing module named 'vtkmodules.vtkFiltersCore' - imported by mne.surface (delayed, optional)
missing module named 'vtkmodules.vtkCommonCore' - imported by mne.surface (delayed, optional)
missing module named 'vtkmodules.vtkCommonDataModel' - imported by mne.surface (delayed, optional)
missing module named 'vtkmodules.util' - imported by mne.surface (delayed, optional)
missing module named 'nibabel.freesurfer' - imported by mne.surface (delayed, conditional)
missing module named pyvista - imported by mne.surface (delayed)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named pybv - imported by mne.export._brainvision (top-level)
missing module named EDFlib - imported by mne.export._edf (top-level)
missing module named 'eeglabio.epochs' - imported by mne.export._eeglab (top-level)
missing module named eeglabio - imported by mne.export._eeglab (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.io._mmio (top-level)
missing module named 'sklearn.metrics.scorer' - imported by mne.fixes (delayed, optional)
missing module named openmeeg - imported by mne.bem (delayed, optional)
missing module named cupy - imported by pyqtgraph.util.cupy_helper (delayed, conditional, optional), mne.cuda (delayed, conditional, optional)
excluded module named mne.datasets - imported by mne (top-level), mne.io.tests.test_raw (top-level), mne.io.artemis123.tests.test_artemis123 (top-level), mne.io.boxy.tests.test_boxy (top-level), mne.io.brainvision.tests.test_brainvision (top-level), mne.io.bti.tests.test_bti (top-level), mne.io.cnt.tests.test_cnt (top-level), mne.io.ctf.tests.test_ctf (top-level), mne.io.curry.tests.test_curry (top-level), mne.io.edf.tests.test_edf (top-level), mne.io.edf.tests.test_gdf (top-level), mne.io.eeglab.tests.test_eeglab (top-level), mne.io.fieldtrip.tests.test_fieldtrip (top-level), mne.io.fiff.tests.test_raw_fiff (top-level), mne.io.nedf.tests.test_nedf (top-level), mne.io.tests.test_meas_info (top-level), mne.io.tests.test_pick (top-level), mne.io.tests.test_read_raw (top-level), mne.io.tests.test_reference (top-level), mne.io.tests.test_what (top-level), mne.channels.tests.test_channels (top-level), mne.channels.tests.test_interpolation (top-level), mne.channels.tests.test_montage (top-level), mne.preprocessing.tests.test_annotate_amplitude (top-level), mne.preprocessing.tests.test_artifact_detection (top-level), mne.preprocessing.tests.test_csd (top-level), mne.preprocessing.tests.test_css (top-level), mne.preprocessing.tests.test_eeglab_infomax (top-level), mne.preprocessing.tests.test_fine_cal (top-level), mne.preprocessing.tests.test_maxwell (top-level), mne.preprocessing.tests.test_hfc (top-level), mne.preprocessing.tests.test_ica (top-level), mne.preprocessing.tests.test_otp (top-level), mne.preprocessing.tests.test_regress (top-level), mne.preprocessing.tests.test_ssp (top-level)
missing module named 'mne.tests.test_annotations' - imported by mne.io.ctf.tests.test_ctf (top-level), mne.io.edf.tests.test_edf (top-level), mne.preprocessing.tests.test_artifact_detection (top-level)
missing module named picard - imported by mne.preprocessing.ica (delayed, conditional)
missing module named 'mne.datasets.testing' - imported by mne.io.egi.tests.test_egi (top-level), mne.io.eximia.tests.test_eximia (top-level), mne.io.eyelink.tests.test_eyelink (top-level), mne.io.kit.tests.test_kit (top-level), mne.io.nirx.tests.test_nirx (top-level), mne.io.persyst.tests.test_persyst (top-level), mne.io.snirf.tests.test_snirf (top-level), mne.preprocessing.eyetracking.tests.test_calibration (top-level), mne.preprocessing.eyetracking.tests.test_pupillometry (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional), tqdm.version (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named importlib_resources - imported by jaraco.text (optional), matplotlib.style.core (conditional), mne.utils.misc (optional), tqdm.cli (delayed, conditional, optional)
missing module named h5py - imported by pyqtgraph.metaarray.MetaArray (optional), pyqtgraph.exporters.HDF5Exporter (delayed), mne.io.snirf._snirf (delayed), mne.utils.check (delayed)
missing module named mne.__file__ - imported by mne (top-level), mne.channels.tests.test_montage (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named paramiko - imported by pooch.downloaders (optional)
missing module named xxhash - imported by pooch.hashes (optional)
missing module named mne.channels.__file__ - imported by mne.channels (top-level), mne.channels._standard_montage_utils (top-level)
missing module named pymatreader - imported by mne.io.fieldtrip.tests.helpers (delayed)
missing module named mffpy - imported by mne.io.egi.egimff (delayed, optional)
missing module named shiboken2 - imported by pyqtgraph.Qt (conditional), matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named sip - imported by pyqtgraph.Qt (conditional, optional), pyqtgraph.debug (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), pstats (conditional, optional), site (delayed, optional), rlcompleter (optional), code (delayed, conditional, optional)
missing module named 'wheel.macosx_libfile' - imported by setuptools.command.bdist_wheel (delayed, conditional)
missing module named 'wheel.wheelfile' - imported by setuptools.command.bdist_wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named org - imported by pickle (optional)
missing module named 'tornado.gen' - imported by joblib._dask (conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named distributed - imported by joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named dask - imported by joblib._dask (optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'numpy.lib.array_utils' - imported by joblib._memmapping_reducer (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named lz4 - imported by joblib.compressor (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named 'winrt.windows.system' - imported by winrt._winrt_windows_storage_streams (top-level)
missing module named 'winrt.windows.networking' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named 'winrt.windows.devices.radios' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named 'winrt.windows.ui' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.security' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.applicationmodel' - imported by winrt._winrt_windows_devices_enumeration (top-level)
missing module named 'winrt.windows.devices.bluetooth.rfcomm' - imported by winrt._winrt_windows_devices_bluetooth (top-level)
missing module named libdispatch - imported by bleak.backends.corebluetooth.CentralManagerDelegate (top-level)
missing module named CoreBluetooth - imported by bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.utils (top-level), bleak.backends.corebluetooth.client (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level)
missing module named objc - imported by bleak.backends.corebluetooth (top-level), bleak.backends.corebluetooth.scanner (top-level), bleak.backends.corebluetooth.CentralManagerDelegate (top-level), bleak.backends.corebluetooth.PeripheralDelegate (top-level)
missing module named 'dbus_fast.message' - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.signals (top-level), bleak.backends.bluezdbus.utils (top-level)
missing module named 'dbus_fast.constants' - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.utils (top-level)
missing module named 'dbus_fast.auth' - imported by bleak.backends.bluezdbus.utils (top-level)
missing module named 'dbus_fast.validators' - imported by bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.errors' - imported by bleak.backends.bluezdbus.signals (top-level)
missing module named 'dbus_fast.service' - imported by bleak.backends.bluezdbus.advertisement_monitor (top-level)
missing module named 'dbus_fast.aio' - imported by bleak.backends.bluezdbus.manager (top-level), bleak.backends.bluezdbus.signals (top-level)
missing module named dbus_fast - imported by bleak.backends.bluezdbus.client (top-level), bleak.backends.bluezdbus.manager (top-level), bleak.backends.bluezdbus.advertisement_monitor (top-level), bleak.backends.bluezdbus.scanner (top-level)
missing module named 'android.permissions' - imported by bleak.backends.p4android.scanner (top-level)
missing module named 'android.broadcast' - imported by bleak.backends.p4android.client (top-level), bleak.backends.p4android.scanner (top-level)
missing module named 'dbus_fast.signature' - imported by bleak.backends.bluezdbus.client (top-level)
missing module named async_timeout - imported by bleak (conditional), bleak.backends.bluezdbus.client (conditional), bleak.backends.p4android.scanner (conditional), bleak.backends.corebluetooth.CentralManagerDelegate (conditional), bleak.backends.winrt.util (conditional), bleak.backends.corebluetooth.PeripheralDelegate (conditional), bleak.backends.winrt.client (conditional)
missing module named 'pydoc_data.topics' - imported by pydoc (delayed, optional)
missing module named 'mne_qt_browser._pg_figure' - imported by mne.viz._scraper (delayed)
missing module named sphinx_gallery - imported by mne.viz._scraper (delayed)
excluded module named mne.gui - imported by mne (top-level)
excluded module named mne.commands - imported by mne (top-level)
missing module named colorcet - imported by pyqtgraph.colormap (delayed, conditional, optional), pyqtgraph.widgets.ColorMapMenu (delayed)
missing module named pyqtgraph.PlotItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level), pyqtgraph.exporters.HDF5Exporter (top-level), pyqtgraph.exporters.Matplotlib (top-level)
missing module named pyqtgraph.ErrorBarItem - imported by pyqtgraph (top-level), pyqtgraph.exporters.CSVExporter (top-level)
missing module named PyQt5 - imported by pyqtgraph.Qt (conditional, optional), pyqtgraph.debug (delayed, optional)
missing module named 'h5py.highlevel' - imported by pyqtgraph.metaarray.MetaArray (conditional, optional)
excluded module named PySide6.QtOpenGL - imported by PySide6.QtOpenGLWidgets (top-level)
missing module named 'PySide2.QtWidgets' - imported by pyqtgraph.Qt (conditional)
missing module named 'PySide2.QtGui' - imported by pyqtgraph.Qt (conditional)
missing module named PySide2 - imported by pyqtgraph.Qt (conditional, optional), pyqtgraph.Qt.internals (conditional)
missing module named 'PyQt6.QtWidgets' - imported by pyqtgraph.Qt (conditional)
missing module named 'PyQt6.QtGui' - imported by pyqtgraph.Qt (conditional)
missing module named PyQt6 - imported by pyqtgraph.Qt (conditional, optional)
missing module named 'PyQt5.QtWidgets' - imported by pyqtgraph.Qt (conditional)
missing module named 'PyQt5.QtGui' - imported by pyqtgraph.Qt (conditional)
missing module named pyside2uic - imported by pyqtgraph.Qt (delayed, conditional, optional)
missing module named OpenGL - imported by pyqtgraph.graphicsItems.PlotCurveItem (delayed)
missing module named 'OpenGL.GL' - imported by pyqtgraph.widgets.RawImageWidget (optional)
missing module named metaarray - imported by pyqtgraph.widgets.DataTreeWidget (optional)
missing module named bottleneck - imported by pyqtgraph.imageview.ImageView (optional)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.NINF - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.hypot - imported by numpy (top-level), scipy.stats._morestats (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), scipy.signal._waveforms (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arctan - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named yaml - imported by numpy.__config__ (delayed), scipy.__config__ (delayed)
missing module named numpy.uint - imported by numpy (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
