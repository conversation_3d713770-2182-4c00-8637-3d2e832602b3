('D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\脑机接口康复训练系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('O', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', 'D:\\NK_Python\\脑机接口康复训练\\main.py', 'PYSOURCE-1')],
 'python311.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
