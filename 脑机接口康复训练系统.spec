# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_submodules
from PyInstaller.utils.hooks import copy_metadata

datas = [('assets', 'assets'), ('config', 'config'), ('data', 'data'), ('icons', 'icons'), ('libs', 'libs'), ('ShuJu.db', '.')]
hiddenimports = ['PySide6', 'shiboken6', 'PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'PySide6.QtNetwork', 'PySide6.QtSql', 'numpy', 'numpy.core', 'numpy.core.multiarray', 'numpy.lib', 'numpy.linalg', 'numpy.fft', 'numpy.random', 'scipy', 'scipy.sparse', 'scipy.sparse.linalg', 'scipy.spatial', 'scipy.spatial.distance', 'scipy.signal', 'scipy.stats', 'scipy.linalg', 'pandas', 'pandas.core', 'matplotlib', 'matplotlib.pyplot', 'matplotlib.figure', 'matplotlib.backends', 'matplotlib.backends.backend_qt5agg', 'matplotlib.backends.backend_agg', 'pyqtgraph', 'pyqtgraph.graphicsItems', 'pyqtgraph.graphicsItems.PlotItem', 'pyqtgraph.graphicsItems.ViewBox', 'pyqtgraph.graphicsItems.AxisItem', 'pyqtgraph.graphicsItems.PlotCurveItem', 'pyqtgraph.graphicsItems.PlotDataItem', 'pyqtgraph.graphicsItems.GraphicsObject', 'pyqtgraph.graphicsItems.GraphicsWidget', 'pyqtgraph.widgets', 'pyqtgraph.widgets.PlotWidget', 'pyqtgraph.widgets.GraphicsLayoutWidget', 'pyqtgraph.Qt', 'pyqtgraph.Qt.QtCore', 'pyqtgraph.Qt.QtGui', 'pyqtgraph.Qt.QtWidgets', 'pyqtgraph.functions', 'pyqtgraph.Point', 'pyqtgraph.Vector', 'pyqtgraph.Transform3D', 'pyqtgraph.SRTTransform3D', 'pyqtgraph.debug', 'pyqtgraph.reload', 'pyqtgraph.colormap', 'pyqtgraph.parametertree', 'mne', 'mne.io', 'mne.utils', 'mne.utils._testing', 'mne.preprocessing', 'mne.viz', 'mne.channels', 'mne.filter', 'unittest', 'unittest.mock', 'collections', 'collections.abc', 'functools', 'itertools', 'warnings', 'inspect', 'pydoc', 'bleak', 'bleak.backends', 'bleak.backends.winrt', 'PIL', 'PIL.Image', 'cryptography', 'cryptography.fernet', 'sqlite3', 'sklearn', 'sklearn.base', 'sklearn.linear_model', 'sklearn.svm', 'sklearn.ensemble', 'sklearn.model_selection', 'sklearn.metrics', 'sklearn.preprocessing', 'sklearn.pipeline', 'sklearn.decomposition', 'sklearn.discriminant_analysis', 'joblib', 'pickle', 'json', 'pathlib', 'setuptools', 'jaraco', 'jaraco.text', 'jaraco.functools', 'jaraco.collections', 'more_itertools', 'app.application', 'app.background_loader', 'app.config', 'core.database', 'core.network_config', 'core.simple_voice', 'core.udp_communicator', 'services.auth_service', 'services.api_client', 'services.patient_service', 'services.reference_data_service', 'services.treatment_service', 'services.user_service', 'services.training_session_manager', 'services.classifier_training_manager', 'services.weighted_voting_classifier', 'services.feature_extraction', 'services.feature_extraction.base_extractor', 'services.feature_extraction.individual_feature_manager', 'services.feature_extraction.config', 'services.feature_extraction.fbcsp_extractor', 'services.feature_extraction.riemannian_extractor', 'services.feature_extraction.tef_extractor', 'services.feature_extraction.plv_extractor', 'services.feature_extraction.tangent_space_extractor', 'services.feature_extraction.utils', 'services.feature_extraction.utils.signal_utils', 'services.feature_extraction.utils.validation_utils', 'services.stimulation', 'services.stimulation.stimulation_device', 'services.bluetooth', 'services.bluetooth.standard_bleak_manager', 'services.eeg_processing', 'services.eeg_processing.eeg_data_processor', 'services.eeg_preprocessing', 'services.eeg_preprocessing.kalman_processor', 'services.eeg_preprocessing.preprocessing_config', 'ui.login_window', 'ui.main_window', 'ui.components.modern_card', 'ui.components.parameter_adjuster', 'ui.components.no_wheel_widgets', 'ui.components.mne_topography_widget', 'ui.components.pyqtgraph_curves_widget', 'ui.themes.theme_manager', 'utils.db_helpers', 'utils.chart_helpers', 'utils.user_helpers']
datas += copy_metadata('PySide6')
datas += copy_metadata('shiboken6')
datas += copy_metadata('mne')
datas += copy_metadata('numpy')
datas += copy_metadata('scipy')
datas += copy_metadata('matplotlib')
datas += copy_metadata('pyqtgraph')
datas += copy_metadata('setuptools')
datas += copy_metadata('jaraco.text')
hiddenimports += collect_submodules('PySide6.QtCore')
hiddenimports += collect_submodules('PySide6.QtGui')
hiddenimports += collect_submodules('PySide6.QtWidgets')
hiddenimports += collect_submodules('PySide6.QtNetwork')
hiddenimports += collect_submodules('PySide6.QtSql')
hiddenimports += collect_submodules('shiboken6')
hiddenimports += collect_submodules('mne.io')
hiddenimports += collect_submodules('mne.channels')
hiddenimports += collect_submodules('mne.viz.topomap')
hiddenimports += collect_submodules('mne.utils')
hiddenimports += collect_submodules('mne.preprocessing')
hiddenimports += collect_submodules('mne.filter')


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['docs', 'pyqtgraph.examples', 'pyqtgraph.jupyter', 'pyqtgraph.opengl', 'setuptools.extern', 'tkinter', 'doctest', 'pydoc_data', 'test', 'unittest.test', 'pip', 'wheel', 'PySide6.QtWebEngineWidgets', 'PySide6.QtWebEngineCore', 'PySide6.QtWebChannel', 'PySide6.QtQuick', 'PySide6.QtQml', 'PySide6.QtMultimedia', 'PySide6.QtOpenGL', 'PySide6.scripts', 'PySide6.tools', 'matplotlib.tests', 'numpy.tests', 'scipy.tests', 'sklearn.tests', 'pandas.tests', 'mne.datasets', 'mne.gui', 'mne.commands', 'mne.tests', 'mne.viz.backends.renderer', 'mne.viz._brain', 'pyqtgraph.examples', 'pyqtgraph.jupyter', 'pyqtgraph.opengl'],
    noarchive=False,
    optimize=1,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [('O', None, 'OPTION')],
    exclude_binaries=True,
    name='脑机接口康复训练系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['icons\\ht.png'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='脑机接口康复训练系统',
)
