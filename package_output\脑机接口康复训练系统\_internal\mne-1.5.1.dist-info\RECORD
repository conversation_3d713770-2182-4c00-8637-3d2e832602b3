../../Scripts/mne.exe,sha256=53T_UN7MamfFqsnGDEclpOPoluKUrqVBc9q_q3pv4Dc,108406
mne-1.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mne-1.5.1.dist-info/LICENSE.txt,sha256=oLbT8M9QbedOvE2bj4N3L50ZTFpfpiwRVl4NS6hbcjk,1527
mne-1.5.1.dist-info/METADATA,sha256=AP2oTOySAzOckuhzJvmhjmgrvccKpKaHfBLF-cWSPLw,8308
mne-1.5.1.dist-info/RECORD,,
mne-1.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne-1.5.1.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
mne-1.5.1.dist-info/entry_points.txt,sha256=rzMTXvHakBK6DsYhr56cYWGA5ST2R5rw8NQO3JPAGFI,48
mne-1.5.1.dist-info/top_level.txt,sha256=wee6ngZmcsbDt015kjZ172Kohfye3HfGBdpfo7ZKgDo,4
mne/__init__.py,sha256=gWFBPLfg1Km-Huu8q5EF8OAyIbmWE6qwuf3ReordP98,5638
mne/__main__.py,sha256=9DCAmRtOB8JppQy_HXnIXie7T_XQZbzdfJWAYsYFS28,144
mne/__pycache__/__init__.cpython-311.pyc,,
mne/__pycache__/__main__.cpython-311.pyc,,
mne/__pycache__/_freesurfer.cpython-311.pyc,,
mne/__pycache__/_ola.cpython-311.pyc,,
mne/__pycache__/_version.cpython-311.pyc,,
mne/__pycache__/annotations.cpython-311.pyc,,
mne/__pycache__/baseline.cpython-311.pyc,,
mne/__pycache__/bem.cpython-311.pyc,,
mne/__pycache__/chpi.cpython-311.pyc,,
mne/__pycache__/conftest.cpython-311.pyc,,
mne/__pycache__/coreg.cpython-311.pyc,,
mne/__pycache__/cov.cpython-311.pyc,,
mne/__pycache__/cuda.cpython-311.pyc,,
mne/__pycache__/defaults.cpython-311.pyc,,
mne/__pycache__/dipole.cpython-311.pyc,,
mne/__pycache__/epochs.cpython-311.pyc,,
mne/__pycache__/event.cpython-311.pyc,,
mne/__pycache__/evoked.cpython-311.pyc,,
mne/__pycache__/filter.cpython-311.pyc,,
mne/__pycache__/fixes.cpython-311.pyc,,
mne/__pycache__/label.cpython-311.pyc,,
mne/__pycache__/misc.cpython-311.pyc,,
mne/__pycache__/morph.cpython-311.pyc,,
mne/__pycache__/morph_map.cpython-311.pyc,,
mne/__pycache__/parallel.cpython-311.pyc,,
mne/__pycache__/proj.cpython-311.pyc,,
mne/__pycache__/rank.cpython-311.pyc,,
mne/__pycache__/source_estimate.cpython-311.pyc,,
mne/__pycache__/source_space.cpython-311.pyc,,
mne/__pycache__/surface.cpython-311.pyc,,
mne/__pycache__/transforms.cpython-311.pyc,,
mne/_freesurfer.py,sha256=bNssqDl0sn0HdZKJbSTEs8NQA3qxjPGky0A_Let2pWA,27708
mne/_ola.py,sha256=dBf9Lngl9UgTFFxiyzxuG9YQ180C1bdKbdKdl25O2OU,19447
mne/_version.py,sha256=NPuGXipKrWdU2C5pwDP9Mtb4OeAq4KT48OR8CgCEm0o,160
mne/annotations.py,sha256=D8x73g2F4L_gjcPPsBigu3rbk0Pn_vhMQu_A7wCM9sQ,64176
mne/baseline.py,sha256=_vsnWiJzo30X25Qaxg1cnyLWiQDBveMHXlu8UiSb72I,6831
mne/beamformer/__init__.py,sha256=1jfFP4avBN53V3AHv2T9HFEwp7jlSlg2DeJ9wIcXghE,474
mne/beamformer/__pycache__/__init__.cpython-311.pyc,,
mne/beamformer/__pycache__/_compute_beamformer.cpython-311.pyc,,
mne/beamformer/__pycache__/_dics.cpython-311.pyc,,
mne/beamformer/__pycache__/_lcmv.cpython-311.pyc,,
mne/beamformer/__pycache__/_rap_music.cpython-311.pyc,,
mne/beamformer/__pycache__/resolution_matrix.cpython-311.pyc,,
mne/beamformer/_compute_beamformer.py,sha256=Y6XsXwQ6DEukvmLQs73a4LHvIu1XX_V87_7_dUCs0p0,20475
mne/beamformer/_dics.py,sha256=oPZpoNTbX36f83DPbDzfmCDyXLUM3kR_pEUqtCCeqZQ,21949
mne/beamformer/_lcmv.py,sha256=CgmOgBUnWr8V35x6xfe-wBHqkuNXiOBb4bKJ6-Zjr04,15860
mne/beamformer/_rap_music.py,sha256=DRNWVopHx4sN9ExupKDwQu1ZBltzuvqc1KRcaW-23Wc,10024
mne/beamformer/resolution_matrix.py,sha256=oX61sgzWGstDz_oqD6Q4lfY3mHUdtRl9BSmCoHGyvY4,2833
mne/beamformer/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/beamformer/tests/__pycache__/__init__.cpython-311.pyc,,
mne/beamformer/tests/__pycache__/test_dics.cpython-311.pyc,,
mne/beamformer/tests/__pycache__/test_external.cpython-311.pyc,,
mne/beamformer/tests/__pycache__/test_lcmv.cpython-311.pyc,,
mne/beamformer/tests/__pycache__/test_rap_music.cpython-311.pyc,,
mne/beamformer/tests/__pycache__/test_resolution_matrix.cpython-311.pyc,,
mne/beamformer/tests/test_dics.py,sha256=qFregW3EuUw4WAL8RPh2OF1ArG5o_lNh08lC_hTbkTo,35188
mne/beamformer/tests/test_external.py,sha256=_90irsgeSy3OgulRAHZh2o6noblNvRA2fw5TiIUVMgQ,4127
mne/beamformer/tests/test_lcmv.py,sha256=VUpKzSvGB47SEfyKrmsMICL1NcMecrNqaTbxL0L8t5s,42976
mne/beamformer/tests/test_rap_music.py,sha256=SNOmMlOgIFg7gTqnAK73KCagtZhtJEh9Vl4uDyMdcAI,8040
mne/beamformer/tests/test_resolution_matrix.py,sha256=5xlY2diiz9uRX0iKQD_NsXdIjpDPhpz7uopiRl79LR8,3313
mne/bem.py,sha256=52CWGaJ0by9M44mOBXSG0J-tnPrbxRe2JDxLKK8oN-w,86941
mne/channels/__init__.py,sha256=XwIMz9wwr_AYZV7n4wf6F-6v0O9lQZnkpXKn9z0l4nw,1854
mne/channels/__pycache__/__init__.cpython-311.pyc,,
mne/channels/__pycache__/_dig_montage_utils.cpython-311.pyc,,
mne/channels/__pycache__/_standard_montage_utils.cpython-311.pyc,,
mne/channels/__pycache__/channels.cpython-311.pyc,,
mne/channels/__pycache__/interpolation.cpython-311.pyc,,
mne/channels/__pycache__/layout.cpython-311.pyc,,
mne/channels/__pycache__/montage.cpython-311.pyc,,
mne/channels/_dig_montage_utils.py,sha256=NgbxwWlUHr52rAF3SadYehs-oWGwNfEbjeayzRpWGsM,3120
mne/channels/_standard_montage_utils.py,sha256=Glb-P9jXnVwpSQdGrdqGwcFp8fS5T7jCNmgZtFJPa00,14274
mne/channels/channels.py,sha256=CvtkUCQkUxezHC3RNKY-UjqtQWsbWnndWYJQZTNVptw,70599
mne/channels/data/layouts/CTF-275.lout,sha256=wPWTlglEdlQ_9-q4wDyIlYTZ638ERjCsU8fMQiewOR0,14061
mne/channels/data/layouts/CTF151.lay,sha256=vQ-IGk2qpIREAqkY7SoDDhw3-PzXckBu7fctF_EKk4o,7154
mne/channels/data/layouts/CTF275.lay,sha256=P6U1mGr0660fQiXVEfEy9HSANly_cvTjkrLWiDTRSUA,12807
mne/channels/data/layouts/EEG1005.lay,sha256=9eVthEaHniKh0W39i4uS0beVXwWPyYDKlinINuQuU_k,15343
mne/channels/data/layouts/EGI256.lout,sha256=aY6C0V9exx6LakkE7tnI-pMIlSVGMzrE6IzYB8dxUUs,15526
mne/channels/data/layouts/GeodesicHeadWeb-130.lout,sha256=R2Q9IQwkajIfZPZZ1AYkn_9S-MDLghLrHYYZSQTajNs,5823
mne/channels/data/layouts/GeodesicHeadWeb-280.lout,sha256=eyZtkkGg3ShlDVyeNytmkCUL-sw3cRutlSpcCbLK7aw,12573
mne/channels/data/layouts/KIT-125.lout,sha256=96DLnRR9aVTbeCvSVtb521FwXhnMcFNqhKfEdbwD4Xk,6036
mne/channels/data/layouts/KIT-157.lout,sha256=bV_TgemaaIWTJnQbR5zrAriPAjgTYlrycAJ_AOMmHHk,7562
mne/channels/data/layouts/KIT-160.lay,sha256=5UOjubr2JllNFsOUg7kLGd9vn3EPfeL2TYQBIfaF7Kw,10895
mne/channels/data/layouts/KIT-AD.lout,sha256=MDxJALJiW7Leh0GJvNZkxSnp3AgnmfeTiq5s7FUN7pE,10020
mne/channels/data/layouts/KIT-AS-2008.lout,sha256=P2Gz6mIWHuCHNf4Fwn0Isi6WAWZG1irg0x2_PT1LiUQ,7572
mne/channels/data/layouts/KIT-UMD-3.lout,sha256=OHwn_Vo9q1Q8KKXBArw4ULMnc-6Hcs_vSJXwiFsNipo,7572
mne/channels/data/layouts/Neuromag_122.lout,sha256=QSkJk2ueWJnQUd1YyUJ6r_n0ROOik5vPBVEc8-1H2Cg,4406
mne/channels/data/layouts/Vectorview-all.lout,sha256=EdUPCgsYe8jfqbHgonaMUE_xNXjYH14JHEsw3o8qjOg,16054
mne/channels/data/layouts/Vectorview-grad.lout,sha256=3QRcMhau5BqyRarMijqGKR9mKEr96ZwFgCSaqE8v3U8,10704
mne/channels/data/layouts/Vectorview-grad_norm.lout,sha256=zFxGKfbQfsfBU3B2saGoRUDRvBPcclcXaly1zS6nZFU,5256
mne/channels/data/layouts/Vectorview-mag.lout,sha256=0Rz67KXBl9TUyFB33rQdKGvIQeo-f6eGYVWF6v2FIJA,5358
mne/channels/data/layouts/biosemi.lay,sha256=ZwxsKUntFW5sRwLzl5QtzQlNt_GeMbVkkc5OM9GwgVc,2770
mne/channels/data/layouts/magnesWH3600.lout,sha256=t2d6GCwKiEG2PJv_cmo87W4l5MJCsx5yY0FtrgJNTSM,11940
mne/channels/data/montages/EGI_256.csd,sha256=4s4evL6M3Sqvd8U2-upn5aGixgHrMnQH7kA0qdM6B44,24999
mne/channels/data/montages/GSN-HydroCel-128.sfp,sha256=7dC9jmkO1P9rva_XM0LGnRrTkJbB7X8Mdr1t2ZfXZ6I,5297
mne/channels/data/montages/GSN-HydroCel-129.sfp,sha256=l1HS5kB-0JGkIkn8QbSu6c70TMNmbbDwvn6U6uDk_6E,5316
mne/channels/data/montages/GSN-HydroCel-256.sfp,sha256=Y9Pedj8702u5CWmJqCgGjty2D-qBYH3blgItO9ISbhw,7796
mne/channels/data/montages/GSN-HydroCel-257.sfp,sha256=cNYphH2QU0kSN9H1LkqfwuVr_Wiikp7irOVG2vLJL5E,7823
mne/channels/data/montages/GSN-HydroCel-32.sfp,sha256=BXKXdSgSqYag_F1yeRzLaiTagSVEk5wd4rkiJaqKxc0,1382
mne/channels/data/montages/GSN-HydroCel-64_1.0.sfp,sha256=-zg0mR_Aayl8y-3vkH1AjfdLukjKqLB3AIqzPeIfGlE,2877
mne/channels/data/montages/GSN-HydroCel-65_1.0.sfp,sha256=ZOCKJrOIZ5gqz24JdmU2Yf9poRxdpO6kDfbUObjPlqs,2919
mne/channels/data/montages/artinis-brite23.elc,sha256=pK637v5Q8SGFrfD8qQQ5S99ijQM_rAlMEEqcVIKNkHk,544
mne/channels/data/montages/artinis-octamon.elc,sha256=cU3CT-5OQiKhdr4PzscKzJ4WPxxq4ZFFHxl-SdvedTo,376
mne/channels/data/montages/biosemi128.txt,sha256=Otv7AfTry3e3D4Xc03OtEKYmEi-IkUVb6Re_x4xZTg4,1555
mne/channels/data/montages/biosemi16.txt,sha256=exLw7ryrPfKgfptyhE2SiRCU9SFutjJkBpB2kIld0T4,248
mne/channels/data/montages/biosemi160.txt,sha256=0WHMrGDArib2wmbMle_2icRPYAk_z6kH40xMSP9OlfM,1910
mne/channels/data/montages/biosemi256.txt,sha256=PMat-Cq-LTLo1khVQ6RbP2KkD8VMoaJkId3EFovdQ4A,3354
mne/channels/data/montages/biosemi32.txt,sha256=6zBBN5vvb5jqt3_bIt0O2YUzRONoDwZuEQs-SpkvWLI,453
mne/channels/data/montages/biosemi64.txt,sha256=i9OeySOFSFPB1oMMRs1JQvcA2QupESVB4h5F6ipv168,710
mne/channels/data/montages/brainproducts-RNP-BA-128.txt,sha256=wjN4htWefR-YZTvxXRmSO_U7VYxP9Rd5QaRk19INhDc,2358
mne/channels/data/montages/easycap-M1.txt,sha256=UdAmjVx38A9v4YVybMO3EugMCz8hqZNRyBEDN50QyXQ,947
mne/channels/data/montages/easycap-M10.txt,sha256=LVcj-vA5XRsJlk9OVDM23VzW5whBkwnm3wKNDet1TOc,800
mne/channels/data/montages/easycap-M43.txt,sha256=Y6fEbyYr8-vT54Z_-9gEf4ZbwrBMar6GhV6Nhjja9jM,1189
mne/channels/data/montages/mgh60.elc,sha256=zBA1bozCQi5OWagSd03j_AEj2HJOs75fFDUez3vZ_TI,2110
mne/channels/data/montages/mgh70.elc,sha256=IYoUJZ4Eqmxdzb1S0nYRT2vrLLsEdfX8cssXFKnWezs,2428
mne/channels/data/montages/standard_1005.elc,sha256=HuWRl5RtYt6HLbKsfyJDpZZmLCMUJzZvbcXYTtI3-FM,10478
mne/channels/data/montages/standard_1020.elc,sha256=mTaqQHhizIXwYOnwx0HwHPu9fp_1x1VBoGItHLC02X4,2896
mne/channels/data/montages/standard_alphabetic.elc,sha256=ApncNGWsnmLTIvVlgbx1r4f4Ckf7J71ZOpj9r1c6MPo,2009
mne/channels/data/montages/standard_postfixed.elc,sha256=iNmeCiikMtNFeEG9LYa8hW2dqH1-tZ-dsD0z2LePZro,3066
mne/channels/data/montages/standard_prefixed.elc,sha256=5pgKS9d_2TtkSXtKPGOOWMRQYAIN3njkRBgmjDq8gSk,2305
mne/channels/data/montages/standard_primed.elc,sha256=cweBRRg_HyyqqkdwVV4lzlG0Q9nIK3H5yOhgdpE4EW8,3093
mne/channels/data/neighbors/KIT-157_neighb.mat,sha256=zgzWN9sSoDQ9adgY8pkGqSidynIbf8VmeX1jyYuog-Q,4939
mne/channels/data/neighbors/KIT-208_neighb.mat,sha256=ri0ONFPgR3jsZnFcSyXcF-DhbUk5qx6Me5llsIq9S_M,6636
mne/channels/data/neighbors/KIT-NYU-2019_neighb.mat,sha256=pt1DXUgiXdIPOLb42N9pBraIJCXxCU-EioP7NoAAhGk,4990
mne/channels/data/neighbors/KIT-UMD-1_neighb.mat,sha256=4ES8MSw4BERCgxhWOU4lRy7n5Sa1K16vxhMXSSeuyRQ,4750
mne/channels/data/neighbors/KIT-UMD-2_neighb.mat,sha256=90PFs7z9_CzMUnKRCTDV7s_YdELeQmVwrr9TF1q8TMk,4832
mne/channels/data/neighbors/KIT-UMD-3_neighb.mat,sha256=si09DIUQEAdZNzy9QpGeIuoF9khDj4K-HCCSCqWepBY,4794
mne/channels/data/neighbors/KIT-UMD-4_neighb.mat,sha256=cQTXtCi5-cp01PHvB98pg2endCnHnjEzHadTUDtt3mg,4840
mne/channels/data/neighbors/__init__.py,sha256=hVlZMO-DrktiSc5nxxB1enFwiWU1ErjYhzOlnrshOwQ,487
mne/channels/data/neighbors/__pycache__/__init__.cpython-311.pyc,,
mne/channels/data/neighbors/biosemi16_neighb.mat,sha256=3AryMeOb23O5MqHcPWtUNumKLuPkMRDfYU-N9F4w6cE,511
mne/channels/data/neighbors/biosemi32_neighb.mat,sha256=ZFuurEtDKLr56_z7JXxPNOM4V3ZgQBKVPlq3J50OXP4,942
mne/channels/data/neighbors/biosemi64_neighb.mat,sha256=c001fvlvC_YtT4eNBfpXqRukQUzp9yUGbMRUW80P8y8,1812
mne/channels/data/neighbors/bti148_neighb.mat,sha256=4xX7jc901rdcwXJ5AMD6Qq0DlaZBcNof6-xQjNW9NVA,3920
mne/channels/data/neighbors/bti248_neighb.mat,sha256=ZrXexEotlqtkiokWQaak9zv_PXPHTZQkffUxyMW022Q,6577
mne/channels/data/neighbors/bti248grad_neighb.mat,sha256=AVF5AV1HKN8LrTEdRia3KYI-SZck-GM73s-zaP5ZS34,8337
mne/channels/data/neighbors/ctf151_neighb.mat,sha256=YoCUKM4dYtj-GXZZO4nGne1U-mkDFITYea3k0a10Cgc,4380
mne/channels/data/neighbors/ctf275_neighb.mat,sha256=RdPTpokWjqGNgzDDuEjVZEAv9odUB-I0aDOODBFNOtg,7831
mne/channels/data/neighbors/ctf64_neighb.mat,sha256=f1kvo1d7iXLNuEhAKP6TxK3CuCfZzqKDMPIg8HLdu6k,2397
mne/channels/data/neighbors/easycap128ch-avg_neighb.mat,sha256=QItxppCeR66qp0nZx-SIM8YJMWukGNdOSGrugnWqPW8,3870
mne/channels/data/neighbors/easycap32ch-avg_neighb.mat,sha256=M5aWCpf1Hr2803WWG8_oe2M2vAwsrVDVbSvORNPWWOg,1127
mne/channels/data/neighbors/easycap64ch-avg_neighb.mat,sha256=bHq1sF7y4LLO0Gh-vYYYsVQXO3AXxDzM85_CZVCScvc,1861
mne/channels/data/neighbors/easycapM11_neighb.mat,sha256=pUtJmPnV5UD-x4tIhiFdKcXtIWhhyxXeuvAVrWUHae8,1792
mne/channels/data/neighbors/easycapM14_neighb.mat,sha256=_MA0-fIsOYNcGKXRG7-O96lIWCgr28YIZ-JsELyHr4U,3529
mne/channels/data/neighbors/easycapM15_neighb.mat,sha256=UAHWExTXWtxq6PACH4rxROt6pB3MlqB0TJABQIkqtCY,3906
mne/channels/data/neighbors/easycapM1_neighb.mat,sha256=A9UI9peSBfdGEvKS5Ys4B6BJBOY5_3vKBL2k0IUQSuw,2149
mne/channels/data/neighbors/ecog256_neighb.mat,sha256=YSAyDIiA9-JyLrL4fXUg827W-bfl42KENGvPPbck7WE,6286
mne/channels/data/neighbors/ecog256bipolar_neighb.mat,sha256=_HMWAB5fnshv2aVLekT9UuWpe4W7TFhVaXQCnPJcsWs,4221
mne/channels/data/neighbors/eeg1010_neighb.mat,sha256=xyFrOxxGE1Kc3D78ICpeJiNp7NUg_-egw-9HPgdK8pA,2422
mne/channels/data/neighbors/elec1005_neighb.mat,sha256=8iBtvF-OGomvH7gwehgbqE8ESrXggYCy6NiN92lP550,11892
mne/channels/data/neighbors/elec1010_neighb.mat,sha256=tvOyzgfMRbb_UljN0o58YAGIii4SknDhTvVbfBXMc2w,2390
mne/channels/data/neighbors/elec1020_neighb.mat,sha256=KjEH6nZ1QEf_Wzn2_pLaDpzrEN3ZTz8uHM-Xa87ti80,655
mne/channels/data/neighbors/itab153_neighb.mat,sha256=aInjY3pQf50oA66rr9Xt_3JQxPnJ_2BziRSl5vdkeYs,4045
mne/channels/data/neighbors/itab28_neighb.mat,sha256=NlhdpQjWwgRr2oGbxwN4yIpYrR5o9jxZFgDbbApqLoU,743
mne/channels/data/neighbors/language29ch-avg_neighb.mat,sha256=wsXfV3OF2N_YJHXHRiiiL9XJda3PyRg-VBjWqO2awo4,1135
mne/channels/data/neighbors/mpi_59_channels_neighb.mat,sha256=o8IJ3K-xAC3c2DTbaBzyry8fihMHieytPsIVSqT9Mho,1660
mne/channels/data/neighbors/neuromag122cmb_neighb.mat,sha256=VHT5eUCpkB91f_mKElL5h_W8WnVExTfh83zaKRGPY58,2074
mne/channels/data/neighbors/neuromag306cmb_neighb.mat,sha256=obe8oAB0B97A5nrQ6IOYMucKRO7TdwvJNEoNM818_4E,3685
mne/channels/data/neighbors/neuromag306mag_neighb.mat,sha256=P85uCEJJ93dkyTYkicwCaIzqVSxkmxOsaLrtRU1j2pQ,2753
mne/channels/data/neighbors/neuromag306planar_neighb.mat,sha256=EgP_FfXMU1OsR2xov_gfMt2mFC9GUqk2KWuT6kw5AXI,5580
mne/channels/data/neighbors/yokogawa160_neighb.mat,sha256=TnnWgcShpt5lyl9l-HMudivMueTEf8LMtrO9_9-L0pY,4729
mne/channels/data/neighbors/yokogawa440_neighb.mat,sha256=Ljh4PZHmAXInPsj3TUHoacBM8q1rZKCEtiNsBF9fW0s,15521
mne/channels/interpolation.py,sha256=g-NpKfMLDzJSnpnjwhhpHMF9q8pnhi3U8px3MWQra_w,8789
mne/channels/layout.py,sha256=kRVaBkigZ4g6Pb-st5bPCHCWM0I3-8vLPua9hsGGib4,37881
mne/channels/montage.py,sha256=FHGFg1lQwpJQbziEAtrCx8WgK7-PR4JzSPYpeJXlgds,59235
mne/channels/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/channels/tests/__pycache__/__init__.cpython-311.pyc,,
mne/channels/tests/__pycache__/test_channels.cpython-311.pyc,,
mne/channels/tests/__pycache__/test_interpolation.cpython-311.pyc,,
mne/channels/tests/__pycache__/test_layout.cpython-311.pyc,,
mne/channels/tests/__pycache__/test_montage.cpython-311.pyc,,
mne/channels/tests/__pycache__/test_standard_montage.cpython-311.pyc,,
mne/channels/tests/test_channels.py,sha256=1DbKLlCYVbfEIW19ardGDUCXTcw2BnMw0SH3Xe-k6VY,26700
mne/channels/tests/test_interpolation.py,sha256=7Hu0TVkn1vgDtsAOysLqyihBJ5nKCMzJBaJab0rQmHQ,12498
mne/channels/tests/test_layout.py,sha256=FFGDjT6ir0SXwULE8doDKYNNLhDCHYuUxWq5wTIy32g,14528
mne/channels/tests/test_montage.py,sha256=fNAEG_0gHxQXwM6JGdXHe8UuP8RixLrs5bsJjJjzPBI,72846
mne/channels/tests/test_standard_montage.py,sha256=9IUoS6xKqBMGzKC3YdqBZMymRLpgGlIwxADQjXQ_kz8,10249
mne/chpi.py,sha256=HJLlafQ7G9N0IofUFiEpVlPzMitKgNIWbM-0XubnPI8,56059
mne/commands/__init__.py,sha256=-253tsMEaPA-2rh9likBQ1q9Zs70-Rr3gCuUGZC4TPQ,51
mne/commands/__pycache__/__init__.cpython-311.pyc,,
mne/commands/__pycache__/mne_anonymize.cpython-311.pyc,,
mne/commands/__pycache__/mne_browse_raw.cpython-311.pyc,,
mne/commands/__pycache__/mne_bti2fiff.cpython-311.pyc,,
mne/commands/__pycache__/mne_clean_eog_ecg.cpython-311.pyc,,
mne/commands/__pycache__/mne_compare_fiff.cpython-311.pyc,,
mne/commands/__pycache__/mne_compute_proj_ecg.cpython-311.pyc,,
mne/commands/__pycache__/mne_compute_proj_eog.cpython-311.pyc,,
mne/commands/__pycache__/mne_coreg.cpython-311.pyc,,
mne/commands/__pycache__/mne_flash_bem.cpython-311.pyc,,
mne/commands/__pycache__/mne_freeview_bem_surfaces.cpython-311.pyc,,
mne/commands/__pycache__/mne_kit2fiff.cpython-311.pyc,,
mne/commands/__pycache__/mne_make_scalp_surfaces.cpython-311.pyc,,
mne/commands/__pycache__/mne_maxfilter.cpython-311.pyc,,
mne/commands/__pycache__/mne_prepare_bem_model.cpython-311.pyc,,
mne/commands/__pycache__/mne_report.cpython-311.pyc,,
mne/commands/__pycache__/mne_setup_forward_model.cpython-311.pyc,,
mne/commands/__pycache__/mne_setup_source_space.cpython-311.pyc,,
mne/commands/__pycache__/mne_show_fiff.cpython-311.pyc,,
mne/commands/__pycache__/mne_show_info.cpython-311.pyc,,
mne/commands/__pycache__/mne_surf2bem.cpython-311.pyc,,
mne/commands/__pycache__/mne_sys_info.cpython-311.pyc,,
mne/commands/__pycache__/mne_watershed_bem.cpython-311.pyc,,
mne/commands/__pycache__/mne_what.cpython-311.pyc,,
mne/commands/__pycache__/utils.cpython-311.pyc,,
mne/commands/mne_anonymize.py,sha256=K0BCDVd6EG_fJkTUIXGtWuHtmRKejdUoEwu3MeibkRw,3283
mne/commands/mne_browse_raw.py,sha256=nYaFCJLku_OzvBtN1efWeAUZQhQJ3w0B20bdYlxSaKk,5425
mne/commands/mne_bti2fiff.py,sha256=A6tfrbAfJbTDAWy-wh1bEE332BDV-QhwpHmGpFTZ9IA,3297
mne/commands/mne_clean_eog_ecg.py,sha256=SpsdaMf_Ku30sOuaBpq8eViR4cW9CNo6Qb-86lCLn2Y,5876
mne/commands/mne_compare_fiff.py,sha256=01OF_pgfmoE8zuXy8kol5-oiFafTPGcKwss63ijPuJc,538
mne/commands/mne_compute_proj_ecg.py,sha256=8teqXzl5GeMBRuOHRx_9AaGWXPPZUrtsM7kQ8bFkf44,8344
mne/commands/mne_compute_proj_eog.py,sha256=ZJlcPIy7FgLvnIgNjF9qDN-ZqyRFzPaS7QBbNcww7YM,8354
mne/commands/mne_coreg.py,sha256=n4ePVyX5VM4Ai9tnVS4G4zVtI8sxAPh5cv-1iftDqcM,3904
mne/commands/mne_flash_bem.py,sha256=GW3Zq2qo21rRvxDxXpglY2tao3CaNExr9p-01H1XQOw,5131
mne/commands/mne_freeview_bem_surfaces.py,sha256=Ach5zoupgC8dr7mFMaWE5H9lRXdI59or5tWzUW4br9Y,3294
mne/commands/mne_kit2fiff.py,sha256=rF7w1YZ6d6dM81az1wuTUmicegr-8qB-UrYuE3dntr8,2700
mne/commands/mne_make_scalp_surfaces.py,sha256=By5TTijdz-BIM6bzqFVHXrCwB36GJkk_Ky4SxAmwNH0,2503
mne/commands/mne_maxfilter.py,sha256=frEIFy6A5xI_jy7vNpm7-GAMLqh5t7tFC6V-K13Vu0o,6362
mne/commands/mne_prepare_bem_model.py,sha256=vDs4H35rFug5TnKn_m3bzyfQ5IO6RXYbF6S2ThN2WEY,1654
mne/commands/mne_report.py,sha256=ZXEeju2BhyYdMuk5TLou2uyrjXzxVAdVlqqJz4Dib70,6116
mne/commands/mne_setup_forward_model.py,sha256=Q3tjKvJe-A8y5r4GMWg-kbh7GdN_0FRFPJFat3bf5Xs,4224
mne/commands/mne_setup_source_space.py,sha256=g9eM7VTf1oL2vxoFtu7wvMkeedsq2U4dl8grLEzxwrc,4982
mne/commands/mne_show_fiff.py,sha256=iw4NnW6hgSbgUd7QpwibSao4ILKAauQzE7ytJE8u_Ac,995
mne/commands/mne_show_info.py,sha256=7Lvz-GpoQWw53wRJQmq-caJxNA6Fb4u7O1shnE7UQXg,695
mne/commands/mne_surf2bem.py,sha256=VUXIIGNGn9S54Q2cVwpNUb62pN3JuFornbcg-XW9Lp4,1212
mne/commands/mne_sys_info.py,sha256=NdGvRFm9A-ho_EmSUZ2_i4I7LZmRqQnf88aa1t55o3Y,1177
mne/commands/mne_watershed_bem.py,sha256=PcYixL_3HjgkyKLaXMN0w83KfHxNg1MpiCApz_bVcQg,3196
mne/commands/mne_what.py,sha256=suadD1pP-rw66YQtDQxGYBK6aUGDLjgA8bLeB6ih2l4,473
mne/commands/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/commands/tests/__pycache__/__init__.cpython-311.pyc,,
mne/commands/tests/__pycache__/test_commands.cpython-311.pyc,,
mne/commands/tests/test_commands.py,sha256=y6_Jhku2MbN4Nx2kERp-6_lmAMaM_wIscGT5By1wgHk,16859
mne/commands/utils.py,sha256=SlLGJ22WWcTc0JtPspOw9PFwNpMyhwp6tmhKlulPihQ,3102
mne/conftest.py,sha256=rZ8bLRqNY69uUl3Lna59SUVaGgsHPqU4OOXvx8MFdWE,36712
mne/coreg.py,sha256=7dnlafQoJBNxc1JcbyUACvnfOGIHg85C5KU7p0QbV4o,78521
mne/cov.py,sha256=G5A9rV4EqkABFTYK9t700u5wG5cUWsay17fmF2DwmPw,81028
mne/cuda.py,sha256=ih3GuPaYJrN8ajN82fp9cEBg0sm8jC_IsW-aFzTtgt0,12277
mne/data/FreeSurferColorLUT.txt,sha256=Bur4W_4uHEM9I5ixy-cV6caP6gOyNnnTxg0TQjcWn_s,84835
mne/data/__init__.py,sha256=Lbl-KA9Kir00T8Q0S-9WDHR-ItC4rz1wGX4MyQuiNNg,23
mne/data/__pycache__/__init__.cpython-311.pyc,,
mne/data/coil_def.dat,sha256=whv2BbZbxlTDEOv2yV3lFcOIPTynZgNNwnMHk35ZVn4,51080
mne/data/coil_def_Elekta.dat,sha256=jT2u6HnFqivIo7RnSruliqtGDiesqQqJ3kpEMZMXLZQ,5064
mne/data/eegbci_checksums.txt,sha256=FvcCJatoTQCV1gx-aSKmzLzgjMbSAAgrL0MpGqi5e-Q,293567
mne/data/extinction_coef.mat,sha256=a3_g1kr5LlQnQbGgXpxKAiM9CIMp5un275xYlviHGEM,3419
mne/data/fsaverage/fsaverage-fiducials.fif,sha256=AEOJX9MWeHGpR6QwGSKO4wBgwDNZZ99j9XHA5xvzRKM,260
mne/data/fsaverage/fsaverage-head.fif,sha256=so60CCj3G58iVbS6_N_cyntNzZwOVngaRt3cPBSv2_I,97892
mne/data/fsaverage/fsaverage-inner_skull-bem.fif,sha256=f5rr-mkkg0n7RyqGPzS7d7AY8dEzDAImKqLhZ3SxDxs,491944
mne/data/fsaverage/fsaverage-trans.fif,sha256=owACKT2IwaOLWmmYtSh7bf5gGWxsp8nkCB-qvXflAQs,212
mne/data/helmets/122m.fif.gz,sha256=pNBDR81KIahLSb1ParI46HiZmtpxpexYZJRIescKsDk,4706
mne/data/helmets/306m.fif.gz,sha256=JUR-aVcAeThoXR2ngKwBAOHUk6nSVdpQiZbOuaVFVmQ,9462
mne/data/helmets/306m_rt.fif.gz,sha256=cv-NuhiAlAIKfFyIvQuduMOctopD-afM45iylSDSJP4,9443
mne/data/helmets/BabySQUID.fif.gz,sha256=VK_1Yc8LdiR2mddFyp_HwU5dmxuAnXTuZ-j3jo6H_8I,59505
mne/data/helmets/CTF_275.fif.gz,sha256=eAPiMkhOzdHi9Q0XvWT6yUWM8SYzQ0XcnhW0WUkLRrk,11157
mne/data/helmets/KIT.fif.gz,sha256=_JRVNQPVEhZQKshywlYXFydKkI8dnHGt9I-Ce4TnW60,9477
mne/data/helmets/Magnes_2500wh.fif.gz,sha256=6TahNUOWqmmdb1CO7kFlZTontaBYzjxRIrcTOTKxqC8,9470
mne/data/helmets/Magnes_3600wh.fif.gz,sha256=h7WETiyboWH4dbxVO6CdVajhdNlSlKMiSdU0MpOaW6k,9475
mne/data/icos.fif.gz,sha256=JCjimEXcbtXEjtMBrG6b9S8Zs-0cTGEe234lzdx-HCU,3732551
mne/data/image/custom_layout.lout,sha256=ZskYBPWbsxwdd3QQOh3ubpuGINE5ThNIDf6PbZoZem8,11190
mne/data/image/mni_brain.gif,sha256=BKiC9kZjhrCqp_g2WmlR64ko-bE4d1iwf6CswXc76H0,12051
mne/data/mne_analyze.sel,sha256=jGeKAvNcgDGPY1HBrajJTqmOWKbUaNA02NEBEoGcTqI,4320
mne/datasets/__init__.py,sha256=oGyCXfeqAPcQlKWU4YXB1LSV9JKB2WVlFgVrum_wBAI,1620
mne/datasets/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/__pycache__/_fetch.cpython-311.pyc,,
mne/datasets/__pycache__/config.cpython-311.pyc,,
mne/datasets/__pycache__/utils.cpython-311.pyc,,
mne/datasets/_fake/__init__.py,sha256=vQiBsHuZkZV6i9luFWrlviVadN3I8HC1pBXSEM_H4VQ,75
mne/datasets/_fake/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/_fake/__pycache__/_fake.cpython-311.pyc,,
mne/datasets/_fake/_fake.py,sha256=-NDERnfFdwORyFEysS9XoUaMEDHbvqdMJV7Qe-wjxJg,844
mne/datasets/_fetch.py,sha256=BLg1fJQg_-dOyljtGqgvNSixTmQbKXKi_wq64FFx9ek,11863
mne/datasets/_fsaverage/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/datasets/_fsaverage/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/_fsaverage/__pycache__/base.cpython-311.pyc,,
mne/datasets/_fsaverage/base.py,sha256=H3OC_joOwitQHuyUKsb-BA2CtBwkKe5I_vNyZoE8pGc,4563
mne/datasets/_fsaverage/bem.txt,sha256=D0mzzzEmfhAAbSaJGGJp-XarHvdpX22OraOgJ3vbwjQ,320
mne/datasets/_fsaverage/root.txt,sha256=SuQqRVNcccb_IaZG6bYJAJKyTN_KtroNEwHriH9zLOA,5795
mne/datasets/_infant/ANTS1-0Months3T.txt,sha256=GnH5-GZwa30kyy0jQRJEJ1a0MhtilFNwwVmfPLGslxo,2499
mne/datasets/_infant/ANTS10-5Months3T.txt,sha256=YYIrUoSdnOWUeXp54d4CTsn-amMFB9R_W7Ny-0-TLpo,2475
mne/datasets/_infant/ANTS12-0Months3T.txt,sha256=JQiSDzWW_yMevfZ7epD2samjO-HGlitBlWwmUnm2ldg,2689
mne/datasets/_infant/ANTS15-0Months3T.txt,sha256=Wqp4F2oZRdwEirlp8czCYMF4EZT0TzCDgBh2moiJrrU,2689
mne/datasets/_infant/ANTS18-0Months3T.txt,sha256=DyFKWOJZWOFHooYmJh6uTW1i88iDwomnGs99VXIVZzk,2689
mne/datasets/_infant/ANTS2-0Months3T.txt,sha256=8i96UnHX2KXiXseFcwdEVBfg0a1RSVsVI4kkATm-31c,2499
mne/datasets/_infant/ANTS2-0Weeks3T.txt,sha256=oWSq4CnyEHN6IToFjYbsicl447HIYN5FWeqLp_Cqrzs,2493
mne/datasets/_infant/ANTS2-0Years3T.txt,sha256=orv71tBsD_-iSOpvPQHOnuL3fiZ5HzzrjjPukzEonWM,2677
mne/datasets/_infant/ANTS3-0Months3T.txt,sha256=wGmM_C-RXvyA7qXyXkmz-Nw0xbWk5rOYHdVbGVDa5Jo,2683
mne/datasets/_infant/ANTS4-5Months3T.txt,sha256=wpYG4pmHgJVgJH1h0_WLCDBdexFQ1Y8Su9gh9hZAzOU,2683
mne/datasets/_infant/ANTS6-0Months3T.txt,sha256=wJClO399i9qkQx8ySuK0IirhKCL2y32gMR_3bsI_mUM,2683
mne/datasets/_infant/ANTS7-5Months3T.txt,sha256=DEmKXvT1rHiMJ0c9ZdnEH2rbif0RnplhIcN6gmERBYg,2683
mne/datasets/_infant/ANTS9-0Months3T.txt,sha256=kLmPA4DjkLklVNw2jYXGJRHwg6u33NFttPDaO7emjQs,2683
mne/datasets/_infant/__pycache__/base.cpython-311.pyc,,
mne/datasets/_infant/base.py,sha256=474YsxQv-I7BQjKVgGcatHkszhwQ8qnS2Vmn8DV9wsI,3866
mne/datasets/_phantom/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/datasets/_phantom/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/_phantom/__pycache__/base.cpython-311.pyc,,
mne/datasets/_phantom/base.py,sha256=Ih_f7o6DP3u5i6UdL3NlBZK7MIIoM0A-4SMo8S9Xj4U,1834
mne/datasets/_phantom/phantom_otaniemi.txt,sha256=hCm1SVT8i7IW70iN7lQpjRG-h7gEM7US4B_cBd8udEA,113
mne/datasets/brainstorm/__init__.py,sha256=wbpxeAybbK_AyY42-le8ASuYdvnWEtnMQ_Iye5qbJCQ,114
mne/datasets/brainstorm/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/brainstorm/__pycache__/bst_auditory.cpython-311.pyc,,
mne/datasets/brainstorm/__pycache__/bst_phantom_ctf.cpython-311.pyc,,
mne/datasets/brainstorm/__pycache__/bst_phantom_elekta.cpython-311.pyc,,
mne/datasets/brainstorm/__pycache__/bst_raw.cpython-311.pyc,,
mne/datasets/brainstorm/__pycache__/bst_resting.cpython-311.pyc,,
mne/datasets/brainstorm/bst_auditory.py,sha256=PC7eukIl2oqvhI8oRHVXX0zU4Y-Ul0Sf4myQiGKxzZI,1789
mne/datasets/brainstorm/bst_phantom_ctf.py,sha256=Q-cB17UPxY5utAzIUjyEvJPNdpWUV3pEv_Cp6wonwsM,1263
mne/datasets/brainstorm/bst_phantom_elekta.py,sha256=9Q97kgfmmsq1DRmmqQuti3tlCcBpN0Jh85B3FgOVhew,1281
mne/datasets/brainstorm/bst_raw.py,sha256=-WpgmVHIuSYlSdDKESf6biJi01vMMRx-dG6MZ2dXlWo,2298
mne/datasets/brainstorm/bst_resting.py,sha256=7JwUH4m-hRdIhvH5AwkqkhApdGtYSLfVS3UXKNrxXHY,1335
mne/datasets/config.py,sha256=rmRvK9i6iFpdlhEJ_ElYs8uRoi3lqUpNjV73mueYH6E,13745
mne/datasets/eegbci/__init__.py,sha256=g_48PsJej7Ec1T13xw3pw1_EPrbZZh4ixFIK2d_QK3c,97
mne/datasets/eegbci/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/eegbci/__pycache__/eegbci.cpython-311.pyc,,
mne/datasets/eegbci/eegbci.py,sha256=c6Ayt4U9e6ZmwpBheQNwkswvh44uAbc83XqzJL6_YNA,7854
mne/datasets/eegbci/tests/__pycache__/test_eegbci.cpython-311.pyc,,
mne/datasets/eegbci/tests/test_eegbci.py,sha256=BLIbWLLYF6u9XVl1QRI7VYB0VSRi1xLkgOkX24BsRMo,397
mne/datasets/epilepsy_ecog/__init__.py,sha256=UBjcZVFigFASFHoUnhZRqg7kSsKg3jA7CPK6nR0N0fI,77
mne/datasets/epilepsy_ecog/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/epilepsy_ecog/__pycache__/_data.cpython-311.pyc,,
mne/datasets/epilepsy_ecog/_data.py,sha256=tAkCzca3l7ErTu3YxR8ptn-p3dThs_mfjnyrJJqgo5w,820
mne/datasets/erp_core/__init__.py,sha256=pg7l8CHw-v15P-yznk_x0xGDSb9-oTQIp0GJh4_74k8,74
mne/datasets/erp_core/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/erp_core/__pycache__/erp_core.cpython-311.pyc,,
mne/datasets/erp_core/erp_core.py,sha256=UQpfCMutXDjCgmaQTGM0D54LLDVPJ9eg74XStakeFzQ,682
mne/datasets/eyelink/__init__.py,sha256=ESTyNKL0yABl9vTInKA3rA3a28V0RiB2XN8I6o5UAKE,73
mne/datasets/eyelink/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/eyelink/__pycache__/eyelink.cpython-311.pyc,,
mne/datasets/eyelink/eyelink.py,sha256=0uXh7k1X8DuOM-_3ya66TwoPeot_EXRD64kVzRkOdOY,748
mne/datasets/fieldtrip_cmc/__init__.py,sha256=7JdYTV48_c8DLI_-SR2Ts5WkL1bf7zcOBuk5gCSdLIw,109
mne/datasets/fieldtrip_cmc/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/fieldtrip_cmc/__pycache__/fieldtrip_cmc.cpython-311.pyc,,
mne/datasets/fieldtrip_cmc/fieldtrip_cmc.py,sha256=CYlRfrkw6mvo7xBRaKLDoTxSWIprIAC2mo6YlwucQqA,852
mne/datasets/fnirs_motor/__init__.py,sha256=VW8a5nbcex2lOkTuFWUGTjwdr3oMuWKuipitmGoWbN0,76
mne/datasets/fnirs_motor/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/fnirs_motor/__pycache__/fnirs_motor.cpython-311.pyc,,
mne/datasets/fnirs_motor/fnirs_motor.py,sha256=bKQMC0aZS0f9vfsQHrUY9RAm7XnHZeqr4Mi3gd2qaJs,769
mne/datasets/hf_sef/__init__.py,sha256=q5N7t3hoT-yWtX3TTy6BYiaALK3gxxFGa11wm96xKic,53
mne/datasets/hf_sef/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/hf_sef/__pycache__/hf_sef.cpython-311.pyc,,
mne/datasets/hf_sef/hf_sef.py,sha256=cxNoxVknbFt46_YbWjVnqGOw6933n8Id4JYk9ynpmHM,2867
mne/datasets/kiloword/__init__.py,sha256=7z5zrkBJHvzfQGUfIlaNmg1J-lls7hLdUBuI1Vix1dw,86
mne/datasets/kiloword/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/kiloword/__pycache__/kiloword.cpython-311.pyc,,
mne/datasets/kiloword/kiloword.py,sha256=sTgtSXJjnreWVW17GPNL2V_0YQ39cYtzQ57yImC3KS0,1989
mne/datasets/limo/__init__.py,sha256=enGYHhj3f7b7Q0hIcYJtOC1Uu0xvFWBUGihFhTOXEYo,60
mne/datasets/limo/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/limo/__pycache__/limo.cpython-311.pyc,,
mne/datasets/limo/limo.py,sha256=TrYhwqkNxo_VpLJXmiv9s7EiZIgB0knerb9tAMpl_aA,13058
mne/datasets/misc/__init__.py,sha256=_tfplqGWO5RLMoFx6U86xAAy9Xn1wGGOhdxybQ15L_M,68
mne/datasets/misc/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/misc/__pycache__/_misc.cpython-311.pyc,,
mne/datasets/misc/_misc.py,sha256=4Z76jRoUij_tlbeAcB_VSQxv2kBZE6TnrOP88vRFiHo,851
mne/datasets/mtrf/__init__.py,sha256=FBRdRp1mNbR_PxOKeDI5yxSxc9LhKvsaocckAUsvhDs,62
mne/datasets/mtrf/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/mtrf/__pycache__/mtrf.cpython-311.pyc,,
mne/datasets/mtrf/mtrf.py,sha256=Zj-NYgM-7pl_uqile8GoJB6MZG0KECW1khqdqZjKupQ,765
mne/datasets/multimodal/__init__.py,sha256=VKi3T5hCb-pmnXuEAROJDvHxJec2AcBBk3HTm3bgVP4,74
mne/datasets/multimodal/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/multimodal/__pycache__/multimodal.cpython-311.pyc,,
mne/datasets/multimodal/multimodal.py,sha256=NzHPuIQ1lB6jEphcF3Apo-gaxc5fMxTgUUQPt3DMnpw,879
mne/datasets/opm/__init__.py,sha256=d9Y31_t83cP5d3EH6KuCh3GuLhAbHIWsR2qS4gQmRdU,60
mne/datasets/opm/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/opm/__pycache__/opm.cpython-311.pyc,,
mne/datasets/opm/opm.py,sha256=JDO3CmmEW1a4KPHLGl9y0KwJMJA0lB8P2TjHiDCnClA,839
mne/datasets/phantom_4dbti/__init__.py,sha256=vGRP-AVHyqL59RO1ULKN6gYbjdhSrw8RKRALTIc-fu8,77
mne/datasets/phantom_4dbti/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/phantom_4dbti/__pycache__/phantom_4dbti.cpython-311.pyc,,
mne/datasets/phantom_4dbti/phantom_4dbti.py,sha256=4srARTLKILkLXPMvGW-we5DoghdJT06WJwlGoaWE0Ws,792
mne/datasets/refmeg_noise/__init__.py,sha256=XI-YDSXj1DIZbDDbsvELkAHrwr8RGTjHRtyDtjujSB8,86
mne/datasets/refmeg_noise/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/refmeg_noise/__pycache__/refmeg_noise.cpython-311.pyc,,
mne/datasets/refmeg_noise/refmeg_noise.py,sha256=tN4qwQNv4B41mWOMG8Rt90cnrwtF257NXeFSy4LppTI,770
mne/datasets/sample/__init__.py,sha256=cEf0QFwcznsnKGE-5QTyU9j82zWX3gCLrR3ixdP4Svk,70
mne/datasets/sample/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/sample/__pycache__/sample.cpython-311.pyc,,
mne/datasets/sample/sample.py,sha256=5-CGx0exe4PCnuZPDkH3ORzJ2eTmAN_2ljof1S20t8w,859
mne/datasets/sleep_physionet/SHA1SUMS,sha256=hkJ40ipHzQRlxmQcmRIoPPDTvC5llcGtAABybXHfU0k,24428
mne/datasets/sleep_physionet/__init__.py,sha256=KM3XaLZi_LknyFA17KuHs-u6Y4qtz5PRKT5PLiEEJqk,37
mne/datasets/sleep_physionet/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/sleep_physionet/__pycache__/_utils.cpython-311.pyc,,
mne/datasets/sleep_physionet/__pycache__/age.cpython-311.pyc,,
mne/datasets/sleep_physionet/__pycache__/temazepam.cpython-311.pyc,,
mne/datasets/sleep_physionet/_utils.py,sha256=WP4lkyYzbQSv6DvLX8jofURG-vhFbyE1VTB2P5aMCGE,8092
mne/datasets/sleep_physionet/age.py,sha256=kZz23oKSRc8gcsoybuMOZ-vj36ohyDN0BNaju4YZYOo,5127
mne/datasets/sleep_physionet/age_records.csv,sha256=1TmF55kEYp5wWBpgmyNDHiBFxkkHlGOvhP61yct-G1A,27887
mne/datasets/sleep_physionet/temazepam.py,sha256=JLpxX-4ZT2I0pSBoQm47i4jzJDl2cf05wDh2jRtn7z0,3772
mne/datasets/sleep_physionet/temazepam_records.csv,sha256=1mUun2gbGI7D0RSyCBd2dA795g3BkUx1E1117q_X4zQ,6990
mne/datasets/sleep_physionet/tests/__pycache__/test_physionet.cpython-311.pyc,,
mne/datasets/sleep_physionet/tests/test_physionet.py,sha256=xRuNW3yM1WD-PDUjxLTIV9Q_qthHlxc_Hut7W72xTY4,7238
mne/datasets/somato/__init__.py,sha256=UFYAfmH1vm3wiTG-uKbAh7cMBzV8lQe4KcB3aftg0A4,73
mne/datasets/somato/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/somato/__pycache__/somato.cpython-311.pyc,,
mne/datasets/somato/somato.py,sha256=GzotoSAqvnG9vIhFsxzwCgjdYyEmy76mQRIaU6kxnnQ,859
mne/datasets/spm_face/__init__.py,sha256=NPXeHCvjUZ2jqAVax7iUKFxDkw_OfsIo7Ad6YuyipNo,103
mne/datasets/spm_face/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/spm_face/__pycache__/spm_data.cpython-311.pyc,,
mne/datasets/spm_face/spm_data.py,sha256=ah-63NlJRVy_wnVEafPdZIs8c2ZiMgsPaC1JYI5xHK0,1203
mne/datasets/ssvep/__init__.py,sha256=2vYu7xbty3-u9DOqVlSgIK6QDIdf5Z84XXsElN1kdPs,64
mne/datasets/ssvep/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/ssvep/__pycache__/ssvep.cpython-311.pyc,,
mne/datasets/ssvep/ssvep.py,sha256=k7LK37a2UJk7AlSnJvQ7b4BLvFZNsJikMGTOriQE30I,732
mne/datasets/testing/__init__.py,sha256=JcmUnhkqFcOnAhCc1MoPszQlOGMrKHOeXefGb9Ug04k,150
mne/datasets/testing/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/testing/__pycache__/_testing.cpython-311.pyc,,
mne/datasets/testing/_testing.py,sha256=GOaJiefR4W_Yzw5xtxGJikkWYhIzEfRuvI6h8ezz-Sw,2119
mne/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/datasets/tests/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/tests/__pycache__/test_datasets.cpython-311.pyc,,
mne/datasets/tests/test_datasets.py,sha256=hF7IBn5FRLu7_G4gSkNergE9IGzgSTKhqOR4EQyuMac,12452
mne/datasets/ucl_opm_auditory/__init__.py,sha256=nQ94ieASldQ3cfOtcO9BZd9h_ZOFtQEODvPhjMFMjhE,81
mne/datasets/ucl_opm_auditory/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/ucl_opm_auditory/__pycache__/ucl_opm_auditory.cpython-311.pyc,,
mne/datasets/ucl_opm_auditory/ucl_opm_auditory.py,sha256=5iZS7XJop7muyHS4XQUvPXkgxYJW2EscL97V08yo1-M,800
mne/datasets/utils.py,sha256=yVm2SIyh1QW0MUbezgLYB9WoU48sPy8DO6QliGSyyZY,28103
mne/datasets/visual_92_categories/__init__.py,sha256=6YhSbTs4n6tZe76s9lR2rL_9-wcRiyM_rWCJy0Jxwkw,98
mne/datasets/visual_92_categories/__pycache__/__init__.cpython-311.pyc,,
mne/datasets/visual_92_categories/__pycache__/visual_92_categories.cpython-311.pyc,,
mne/datasets/visual_92_categories/visual_92_categories.py,sha256=2pOkMgxKH8binaKo_vCqF_sB0RVpnOFMiyoMcqOEMBw,2182
mne/decoding/__init__.py,sha256=TpmQx5WrEqpE_gmSPmiq8nH7erDoJBY9rRVs_xNwBRs,624
mne/decoding/__pycache__/__init__.cpython-311.pyc,,
mne/decoding/__pycache__/base.cpython-311.pyc,,
mne/decoding/__pycache__/csp.cpython-311.pyc,,
mne/decoding/__pycache__/ems.cpython-311.pyc,,
mne/decoding/__pycache__/mixin.cpython-311.pyc,,
mne/decoding/__pycache__/receptive_field.cpython-311.pyc,,
mne/decoding/__pycache__/search_light.cpython-311.pyc,,
mne/decoding/__pycache__/ssd.cpython-311.pyc,,
mne/decoding/__pycache__/time_delaying_ridge.cpython-311.pyc,,
mne/decoding/__pycache__/time_frequency.cpython-311.pyc,,
mne/decoding/__pycache__/transformer.cpython-311.pyc,,
mne/decoding/base.py,sha256=OfzJUseYev9-lxXjOnul8tHUAWAfzPbbpZCvLwvlkzc,17156
mne/decoding/csp.py,sha256=nYbYevHFKNxe5mu0QysZFd7YTeLXX9yTzquwk785aHk,29368
mne/decoding/ems.py,sha256=Qh6MuIRKdrP9gkNHhqTHyIGxyKDiGwwUpxCirjTINVs,7623
mne/decoding/mixin.py,sha256=SXOwpEa5FzC-377W5AkKJ7mPWH05JfsmskxfWpM96f4,2863
mne/decoding/receptive_field.py,sha256=22lSHSFj5LJ0wEJ5ueJWvT6gWeIBWPdmTKm6ibwLJ-o,19508
mne/decoding/search_light.py,sha256=b7YeaxM2wp8qK3TpQa12vK8OIWto0_Da044RSym5-F8,27371
mne/decoding/ssd.py,sha256=Utg5RipQ08QHk8YKf6Lsh-iXTUshisc98S3Ayn2dyhc,14734
mne/decoding/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/decoding/tests/__pycache__/__init__.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_base.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_csp.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_ems.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_receptive_field.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_search_light.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_ssd.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_time_frequency.cpython-311.pyc,,
mne/decoding/tests/__pycache__/test_transformer.cpython-311.pyc,,
mne/decoding/tests/test_base.py,sha256=TxPUhJ_J-tVN5RlNI16Ud3CtOyR17xX8SU0fi7agbUM,16194
mne/decoding/tests/test_csp.py,sha256=Zmx_IUqKghSLD8uVK2uDCVOGvFQlG-yKQbSboDZC1oc,13436
mne/decoding/tests/test_ems.py,sha256=6cg0b6qQpr1Bh16NrWPa_buZud5xpUVDBEipQPF7W78,3109
mne/decoding/tests/test_receptive_field.py,sha256=5i2iCH0GeHvZ7t6TIw14cnL_j1giLPA9AXrNgVxHic4,23798
mne/decoding/tests/test_search_light.py,sha256=Mz077sbJNv5RdouNgo_RTapj6Qgb6jnFQ_Whj9koCyc,11636
mne/decoding/tests/test_ssd.py,sha256=N4w_yWrwUAavEBuqcN7fa8Zf6CxygWsPk_XioqYU-fk,13971
mne/decoding/tests/test_time_frequency.py,sha256=-2fA33gwI_iCDqD-_vzPX3E6GwkCJnWZY_jO0bUoXcc,1329
mne/decoding/tests/test_transformer.py,sha256=ScdNLwP2-sZmhLtwGQAkStIgDeJmxkP5ylC0VCJdyew,9654
mne/decoding/time_delaying_ridge.py,sha256=rvvLFW9dfRKCEzNYdUoHwJfdiZeOT6lhRkDsBUueevk,14006
mne/decoding/time_frequency.py,sha256=D6Y8WiVGHrEeMOMGzNL8L_jsi36KDnc2z8nsiW7ll-o,5130
mne/decoding/transformer.py,sha256=HFi7nI7I0c0k4c4kex8JIxX1oy-UtBQLZ5s8mkU_V6M,29890
mne/defaults.py,sha256=IQyC_SIPxw9RqVEVD5pDynZnpjOcGNaZKh5fzRZnpqc,9083
mne/dipole.py,sha256=Ki_JilNA5qD-t-vkjI2gjwsYQL9R3a0azdXAzBm00G8,59756
mne/epochs.py,sha256=b_d_EdHMHkVyFkQeOor-wmJ-PgyCy5Cexoi9AZ3SIRk,157043
mne/event.py,sha256=eBaiVe6bpPLJZI2gFWaIlbxV85Ui0dMg14r6DiblfXA,58258
mne/evoked.py,sha256=NsWYZnz_sWxIQY79vzyk2k6S-OoBDm-aOwICwVOdrso,63086
mne/export/__init__.py,sha256=9yq-PO_XVq5gWpR4kdJgRDaP5HlfBeWXvND89fm7aQo,103
mne/export/__pycache__/__init__.cpython-311.pyc,,
mne/export/__pycache__/_brainvision.cpython-311.pyc,,
mne/export/__pycache__/_edf.cpython-311.pyc,,
mne/export/__pycache__/_eeglab.cpython-311.pyc,,
mne/export/__pycache__/_egimff.cpython-311.pyc,,
mne/export/__pycache__/_export.cpython-311.pyc,,
mne/export/_brainvision.py,sha256=P6KKIMijmlqdV7jqUOamETP5UoouDOLZMTgrGzNL1mc,472
mne/export/_edf.py,sha256=ASa9ZI6T5FiXfOhZCwhbY3_aKFHoc-wKVVkpYMZ5x18,13104
mne/export/_eeglab.py,sha256=37yOG1w-MMBcuuko_6HWonlO0bmcvZBJV1hjjmHZ6_o,2510
mne/export/_egimff.py,sha256=CHow-cb1AZEOjS6hFKFEnT3BvUbaIrqUW2X_DODrlUU,5620
mne/export/_export.py,sha256=F26X94ryMFVeSnPvQS8SgqYNdnHVHwfmRzVjzkeJzM4,6010
mne/export/tests/__pycache__/test_export.cpython-311.pyc,,
mne/export/tests/test_export.py,sha256=A42xVEDFHnT-uEo0jwD19YD_e9Y6rgM8Ycm4M0u4Thc,19485
mne/filter.py,sha256=WiuVX0JHOaAGvevYBEDs-kjfo3WBYCrcvda87mCl4so,95755
mne/fixes.py,sha256=Jj5UpKgFkN7lydCrHn9O7_Evff_dgCUL-KtcrabV-hk,30507
mne/forward/__init__.py,sha256=M2j4W1wIoaTinOf7G5Up6PS2_RlqY92K7yZrAv2wYOI,1123
mne/forward/__pycache__/__init__.cpython-311.pyc,,
mne/forward/__pycache__/_compute_forward.cpython-311.pyc,,
mne/forward/__pycache__/_field_interpolation.cpython-311.pyc,,
mne/forward/__pycache__/_lead_dots.cpython-311.pyc,,
mne/forward/__pycache__/_make_forward.cpython-311.pyc,,
mne/forward/__pycache__/forward.cpython-311.pyc,,
mne/forward/_compute_forward.py,sha256=-6KfpN1kFg_KIkFXmURXseoiTnNdJXrHtetCn-BUPLU,34441
mne/forward/_field_interpolation.py,sha256=hwlkqAD0TVK8C_xuujx0zDfDTMCCv6sYquroTCaIMCo,18407
mne/forward/_lead_dots.py,sha256=JTBqF6rSMajigfbFUDVmCunbLL9y8x4zwgYdy2Qomh0,19463
mne/forward/_make_forward.py,sha256=eR1o57atIQ9ft_M28c5_EAaj4N36_QqQuwn9Hsonh10,31718
mne/forward/forward.py,sha256=lRcNU8OziTy9y1Q9CrF_ku36v5xTzUERCKymrxJxEhc,75021
mne/forward/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/forward/tests/__pycache__/__init__.cpython-311.pyc,,
mne/forward/tests/__pycache__/test_field_interpolation.cpython-311.pyc,,
mne/forward/tests/__pycache__/test_forward.cpython-311.pyc,,
mne/forward/tests/__pycache__/test_make_forward.cpython-311.pyc,,
mne/forward/tests/test_field_interpolation.py,sha256=xGqBQSADnSPTHiqEW3qGsv-4aJejQjXOSUezhvUglgk,11446
mne/forward/tests/test_forward.py,sha256=s2lzOgaPpvGEJFR940ECJhgjohGtQFY7ztvrMbA1SYI,20096
mne/forward/tests/test_make_forward.py,sha256=vsiTndkCIAhU8PFtdWWE4XhrNgezlfcemcPgs87e3Hs,30292
mne/gui/__init__.py,sha256=ibh54MdtkSL1aKUz4ISX48DQeVIXeY_qinURXqZwDTM,10177
mne/gui/__pycache__/__init__.cpython-311.pyc,,
mne/gui/__pycache__/_coreg.cpython-311.pyc,,
mne/gui/_coreg.py,sha256=8z5fAFyMidz7VzPgeUCcufKdCs1SJyuynRQSF0Zp1gk,76082
mne/gui/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/gui/tests/__pycache__/__init__.cpython-311.pyc,,
mne/gui/tests/__pycache__/test_coreg.cpython-311.pyc,,
mne/gui/tests/__pycache__/test_gui_api.cpython-311.pyc,,
mne/gui/tests/test_coreg.py,sha256=LeLxaVN4wTH2631HPxN8AulGiL5CUoic2NGdwJtimn4,12748
mne/gui/tests/test_gui_api.py,sha256=h-jDih_pbSqpgW7wRrDuWAO7cHQMy3JJucWyCUkNPgw,11178
mne/html/d3.v3.min.js,sha256=I3VokevC0pGT7GAfUKEJuWB8TmNuYSiu-84dT8_fGGY,146814
mne/html/mpld3.v0.2.min.js,sha256=MVPek3lj8iKh4wjo9fhI9TKXGq2EHvDyE5ayVTZ2GVM,36228
mne/html_templates/__init__.py,sha256=w2X5uW2nv_EvFAmqK-vcP9t_ijZT6kzkkURsmJF5b00,95
mne/html_templates/__pycache__/__init__.cpython-311.pyc,,
mne/html_templates/__pycache__/_templates.cpython-311.pyc,,
mne/html_templates/_templates.py,sha256=01IGgRFjx2YsrYRK9sxB3s5YddQolUz31fgwtu45nRI,509
mne/html_templates/report/bem.html.jinja,sha256=nXU211sK4FlDUgFvf0lsRZhvDvCQ6zgLZh_US7eK2fE,217
mne/html_templates/report/code.html.jinja,sha256=q9UhFZnAkkxy-Yh6UIypN-emZL0aDvmkZVJQOZTNsaY,172
mne/html_templates/report/footer.html.jinja,sha256=sdK5BBSrpxiItIWdiE2hBUFelhcQldCL9uAwOgC_6vo,287
mne/html_templates/report/forward.html.jinja,sha256=tAgy0sqGMXjp5H0EPANkTOxAPXl5zaFOHneH1wsJBE8,132
mne/html_templates/report/header.html.jinja,sha256=UFY02p19rIdhXv29xT9Hto9iIhYV_MgbC22irn7uF4E,1864
mne/html_templates/report/html.html.jinja,sha256=s97vPCGnTToZbuYKTynAFaIwsvcRHTQhLbz90Y0IRkY,892
mne/html_templates/report/image.html.jinja,sha256=cKd3guIYTGGyy_ah2TBI-uhrfqrJ_owpaLHg8Lhw5Pk,535
mne/html_templates/report/inverse.html.jinja,sha256=xnnRezwpzND3M-_cHAokkFiW2MrCXG7c5_EgGs7xuCE,128
mne/html_templates/report/section.html.jinja,sha256=o5NbGu3vBxYCW1j8zNwXupf9UpfaArT-YObA52MCkj8,993
mne/html_templates/report/slider.html.jinja,sha256=M3Yy5o7rQbd--OhmdDDbZNxWBCqPCnhg8LojMhsOH1Q,2812
mne/html_templates/report/toc.html.jinja,sha256=cWK2McwPAJlCnIHeCO6YFY7y9yNoBUXYDgVI3SWa3Vs,610
mne/html_templates/repr/epochs.html.jinja,sha256=U-ICkhoFiduXtEDMB8uddzpcJwxqLE-b-vr62u_6TZE,599
mne/html_templates/repr/evoked.html.jinja,sha256=qGOwGDKjmapFyv7bEaAMNxw7klLkRw6VfQVcko2Z_1c,689
mne/html_templates/repr/forward.html.jinja,sha256=bRLUEBT6i8rD7dSLwEvK7si6efA8zpAc0gcDLnFyZmw,449
mne/html_templates/repr/ica.html.jinja,sha256=ycXyNiwtXo8YY4CRcTVnjrU8HuoadmywbVU_oALyLo0,811
mne/html_templates/repr/info.html.jinja,sha256=wm_YANbRDMAYZi_QgiUU4cy9L6iLp5QcWn57ZXZP5Do,1836
mne/html_templates/repr/inverse_operator.html.jinja,sha256=DDgd6Qr6lLnGNjDOhkSMjfj3aGkBrAtmKEBl7KjEgXI,354
mne/html_templates/repr/raw.html.jinja,sha256=6UwCBL3oABlgi4ARrlc4mrj4vkZZDFp6JSDJdxzlGPk,256
mne/html_templates/repr/spectrum.html.jinja,sha256=s46C5dJRy-QJn_ET1VajCHYfrGC7VBmAdfeHFsVjwZs,1329
mne/icons/README.rst,sha256=0XlE_g-oJdXQAcuDAup6p5W-jts2pZYrUdi8Wqyv8ag,290
mne/icons/dark/actions/clear.svg,sha256=Y_EjEwNJzB2haOazKJ_yiC2hsxXai4tdJOJPkPnGTww,252
mne/icons/dark/actions/folder.svg,sha256=c8jACCRV85vSOsH7TA0Kmya0jcy3Ehp4yiR-yb3fV6I,276
mne/icons/dark/actions/help.svg,sha256=YP69_Iv0UerUgMQyXxnh6QAs9dtFTJzb9WICFnLBYkc,402
mne/icons/dark/actions/movie.svg,sha256=vOheCVKg9Oykd1LRAsYfAEIRqZ_SMgbIB3vPuYzk6hA,442
mne/icons/dark/actions/pause.svg,sha256=wohxhz3IKd-OPPSEJzMGkbdIv_4r1RHyvcH0yx09UTA,452
mne/icons/dark/actions/play.svg,sha256=wZBQgWzjFqfueIYKfLJbXDHV0Hx1nikmCsCUFBBKzSk,405
mne/icons/dark/actions/reset.svg,sha256=HJFniV5d7EpIsqtGSdLIz7kXymGTAnX6KZElWjAmeY0,372
mne/icons/dark/actions/restore.svg,sha256=eabSOyWUmQ53CY2UL0V2qnXqyY4x8IUo22cM6fNPU38,377
mne/icons/dark/actions/scale.svg,sha256=Po_937wSOY0Pls0T7G3sJcvb46SyzQRvusu-PARDS6w,685
mne/icons/dark/actions/screenshot.svg,sha256=-B0J0XMreDt3g9tU9YylIZyWZYW1_w-MgevMkAVR1CA,396
mne/icons/dark/actions/visibility_off.svg,sha256=SpSAw0-GmMoLAni9wYBwAjGjEw2mkqmbLOiFW4Sr8iA,901
mne/icons/dark/actions/visibility_on.svg,sha256=L8oF6Fx5V8WWoNA-xeGfXmtAW6h8qBJeDIc87-ySVjo,493
mne/icons/dark/index.theme,sha256=orFGpnRC8VfMQK9Kkid6mOlvgxZx4N7E8GF4HYsmBSo,116
mne/icons/light/actions/clear.svg,sha256=JsIhU-6OyeVznG3cVINQjPvMg01gxI1RKZeOc_pGodw,252
mne/icons/light/actions/folder.svg,sha256=2QoDptDVoVFNMlXWKGs1HqaGNssK0ySv0Hb_uXVvRR0,276
mne/icons/light/actions/help.svg,sha256=CMn_c5wkCfhvBEaMDf46ItCMBZsIOgjurUdRoADozaI,402
mne/icons/light/actions/movie.svg,sha256=lCWysTRezAOYhsM9FSbXAim1eKFg0DSQHa9OAyuc8Rg,442
mne/icons/light/actions/pause.svg,sha256=74xJk8b-T3LIc0HjR8OVLERisN5pO9G68C24D85juSQ,452
mne/icons/light/actions/play.svg,sha256=xGuUZGZBuzS_IFxLlqcoUqmRqSzwDvjE8Zm2vSMxR3o,405
mne/icons/light/actions/reset.svg,sha256=rWXowmEjQwIxKjVnkDYSCyuG4PpYmFG-uCoshKE0K9w,372
mne/icons/light/actions/restore.svg,sha256=bqCx1dFsAXuTosh5Z35etdVouZXjYNw6FP0c2tQTd34,377
mne/icons/light/actions/scale.svg,sha256=gWFMhSeqhzg2FKwRESZTayV5vf1L5YhdloWdQHycKo4,685
mne/icons/light/actions/screenshot.svg,sha256=kaT7QcNTxLQ8LLwqdU8VbXErZRwNEZi-_Z09tGAq6ro,396
mne/icons/light/actions/visibility_off.svg,sha256=hTd7Fwdvl748NslyCW3fkj5wXfZohuck6clCUj6ymg0,901
mne/icons/light/actions/visibility_on.svg,sha256=aUNE1K7CSQua73yxlp_pFTl0g1NWLmBw0RHRN39NNHA,493
mne/icons/light/index.theme,sha256=92ZhbH_7lmAloteyYkzk4UYQu8Kr1OMPCdk5pA7xk8E,117
mne/icons/mne_bigsur_icon.png,sha256=W1HWxvcEuFb88uuJ50T-AuGFfk3S5fdCPdNqe54W29o,44028
mne/icons/mne_default_icon.png,sha256=-sXwcNTk6s09cl5g_8I1K5JMiTiNlFmkOPsZUR7-eWA,10369
mne/icons/mne_icon-cropped.png,sha256=CZI6lw3Yb8eljfd9QSGQaKO37ovKMqU_pabubb0ab6k,44818
mne/icons/mne_icon.png,sha256=yeaDviCF-2hU0KVKocGaok5lhJeRLNuM9q8lD7MI5to,31258
mne/icons/mne_splash.png,sha256=roZwx2bDUCjDTnO0Lt77OUZNPVsUADQFfkwMRreo9kc,22283
mne/icons/<EMAIL>,sha256=WzGThNdXpYkxiYe9i6GhOjjYYQ-o93lgHSnUEN44awI,304
mne/icons/<EMAIL>,sha256=W9x8BZuj7ybZmpHJcJA7Li9A2Ga5mUjJPXPh42BuJr4,208
mne/icons/toolbar_separator_horizontal.png,sha256=saDqCirYpRkWoNFtvdc9ION-8ZSLU6SD7Lwd8ImPQWI,151
mne/icons/<EMAIL>,sha256=QrM7z43F0KkSOtzWcfSq9f5NqGZinXhN7sWR451ZYUM,288
mne/icons/<EMAIL>,sha256=e9HXqqUM5Qj-IbbfBnm0e28RNZxAozfrztMZxq5QXxI,192
mne/inverse_sparse/__init__.py,sha256=anMcU5PCQfUW2-wJtP4PqXAwqF4oVYv2YBI5BjQm6Tk,239
mne/inverse_sparse/__pycache__/__init__.cpython-311.pyc,,
mne/inverse_sparse/__pycache__/_gamma_map.cpython-311.pyc,,
mne/inverse_sparse/__pycache__/mxne_debiasing.cpython-311.pyc,,
mne/inverse_sparse/__pycache__/mxne_inverse.cpython-311.pyc,,
mne/inverse_sparse/__pycache__/mxne_optim.cpython-311.pyc,,
mne/inverse_sparse/_gamma_map.py,sha256=CEBH3fOHK5EQNqFdL1wQjufvDCI2Rj9TwB9YtZaZaMI,10211
mne/inverse_sparse/mxne_debiasing.py,sha256=N1_ZyIq8y5aXrgEd9_BRnki_G8Oohx4s5WN1BOQV-SM,3564
mne/inverse_sparse/mxne_inverse.py,sha256=cF24nBWpLNAgYA9G0TzZaGc7bB_3B0LXmAK5eDfLGH4,35157
mne/inverse_sparse/mxne_optim.py,sha256=5W0Pb7q9Xso2DXmzKYFi9x8CEcFA5rtMLqVB-sidX4k,54504
mne/inverse_sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/inverse_sparse/tests/__pycache__/__init__.cpython-311.pyc,,
mne/inverse_sparse/tests/__pycache__/test_gamma_map.cpython-311.pyc,,
mne/inverse_sparse/tests/__pycache__/test_mxne_debiasing.cpython-311.pyc,,
mne/inverse_sparse/tests/__pycache__/test_mxne_inverse.cpython-311.pyc,,
mne/inverse_sparse/tests/__pycache__/test_mxne_optim.cpython-311.pyc,,
mne/inverse_sparse/tests/test_gamma_map.py,sha256=xpt-28Sz7GcRHSfjGLvSR3vSq2sljF6Wtfu6hG8ISaU,6754
mne/inverse_sparse/tests/test_mxne_debiasing.py,sha256=sQgI9G8vpp2j-jvn0PT2M4IEUH447FtDCGpTTAuWpQs,807
mne/inverse_sparse/tests/test_mxne_inverse.py,sha256=mhjYQ2jEhDlD5Lo1tJr76FGAABoyriGW95C3mo48jbQ,19290
mne/inverse_sparse/tests/test_mxne_optim.py,sha256=QGov1Jhxz-tub_5Ut3k2B4khudo_ZJVxQ3gxioh91Vw,14735
mne/io/__init__.py,sha256=BvDwKQyeb8DPksO1Qd09dvAz7-RAo8bQos1gIQPXg6c,2279
mne/io/__pycache__/__init__.cpython-311.pyc,,
mne/io/__pycache__/_digitization.cpython-311.pyc,,
mne/io/__pycache__/_read_raw.cpython-311.pyc,,
mne/io/__pycache__/base.cpython-311.pyc,,
mne/io/__pycache__/compensator.cpython-311.pyc,,
mne/io/__pycache__/constants.cpython-311.pyc,,
mne/io/__pycache__/ctf_comp.cpython-311.pyc,,
mne/io/__pycache__/diff.cpython-311.pyc,,
mne/io/__pycache__/matrix.cpython-311.pyc,,
mne/io/__pycache__/meas_info.cpython-311.pyc,,
mne/io/__pycache__/open.cpython-311.pyc,,
mne/io/__pycache__/pick.cpython-311.pyc,,
mne/io/__pycache__/proc_history.cpython-311.pyc,,
mne/io/__pycache__/proj.cpython-311.pyc,,
mne/io/__pycache__/reference.cpython-311.pyc,,
mne/io/__pycache__/tag.cpython-311.pyc,,
mne/io/__pycache__/tree.cpython-311.pyc,,
mne/io/__pycache__/utils.cpython-311.pyc,,
mne/io/__pycache__/what.cpython-311.pyc,,
mne/io/__pycache__/write.cpython-311.pyc,,
mne/io/_digitization.py,sha256=91fQWyvOprkM-ocGjxKSkgk_-mQapBN1EVvMiWAuqK0,20833
mne/io/_read_raw.py,sha256=aaw56fuqRRVjt3UfDMKp7ECO5QJy45DFdkm1kC0768g,5033
mne/io/array/__init__.py,sha256=sIOXLxdMoAm5qnsdFRcTFz2FXVHufjGsODhI_A-IOnM,120
mne/io/array/__pycache__/__init__.cpython-311.pyc,,
mne/io/array/__pycache__/array.cpython-311.pyc,,
mne/io/array/array.py,sha256=YEAFiaDL-9xUFgLGkgsHsF4GgY8oMCtX9xfg2t7CLnI,3234
mne/io/array/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/array/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/array/tests/__pycache__/test_array.cpython-311.pyc,,
mne/io/array/tests/test_array.py,sha256=TglewpF-fulABihSOYKhhoHT9ljwDGbARse5jxjJ6hw,6407
mne/io/artemis123/__init__.py,sha256=ck505pNUW9O8n0ma5w4ulGGSRxO6kt8neVUm-GtZJAg,156
mne/io/artemis123/__pycache__/__init__.cpython-311.pyc,,
mne/io/artemis123/__pycache__/artemis123.cpython-311.pyc,,
mne/io/artemis123/__pycache__/utils.cpython-311.pyc,,
mne/io/artemis123/artemis123.py,sha256=ZlgmaQkXn6BSeaar_zN8P8X2EK7QMHzuyr7ToTHdmuw,19620
mne/io/artemis123/resources/Artemis123_ChannelMap.csv,sha256=WAKPoj3UOm2cg-x98aeqVM_Wed2TP6uZ_ybwq2d6WeI,9272
mne/io/artemis123/resources/Artemis123_mneLoc.csv,sha256=F_h9Wxe4Q5_gG3CO47rSXUqSM1N_oEr2cOJXd8ZgNUo,25854
mne/io/artemis123/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/artemis123/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/artemis123/tests/__pycache__/test_artemis123.cpython-311.pyc,,
mne/io/artemis123/tests/test_artemis123.py,sha256=T8iy3566DB5o7vuQFK7VFyWWrCx2t5f_rgwZSbePR5I,4407
mne/io/artemis123/utils.py,sha256=oRfqCR6XZpTqwxTnD6HSIqmgYiV0WM_HWhlOMXKmFlM,4365
mne/io/base.py,sha256=yeEWwUoB_66E00nI-vgRN5uJ_IMhiArNC7HYWCMlkkE,106113
mne/io/besa/__init__.py,sha256=1YuixLJZjapyYdkRaDvZ-907mxti-vc3jyQh0SrMp4s,160
mne/io/besa/__pycache__/__init__.cpython-311.pyc,,
mne/io/besa/__pycache__/besa.cpython-311.pyc,,
mne/io/besa/besa.py,sha256=UbgrNUSltvS7qIDLJbtI1v97RjCWeAjMoAY3FVksm9U,8977
mne/io/besa/tests/__pycache__/test_besa.cpython-311.pyc,,
mne/io/besa/tests/test_besa.py,sha256=FMT-iYfQYpF4OnKP_m-XHCqf2JgR4Cpm5wB5Cq0-yfw,2890
mne/io/boxy/__init__.py,sha256=AGuClCpeOIl7uUMrea87THdFZPueeuJeOVsuOKWHbyw,166
mne/io/boxy/__pycache__/__init__.cpython-311.pyc,,
mne/io/boxy/__pycache__/boxy.cpython-311.pyc,,
mne/io/boxy/boxy.py,sha256=ViirqZPUQEzS-p2Kza8BKZMu1R1E0LHbUbQ_5cwMhog,11481
mne/io/boxy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/boxy/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/boxy/tests/__pycache__/test_boxy.cpython-311.pyc,,
mne/io/boxy/tests/test_boxy.py,sha256=DPFUic9m-Ly4W3XirJT1O6rHLfJNm4GSIExUaMp6X4Y,7599
mne/io/brainvision/__init__.py,sha256=4xc9PG3V-hEqPJAwO481B6qWs4PiqBd007CIpSbcCyQ,226
mne/io/brainvision/__pycache__/__init__.cpython-311.pyc,,
mne/io/brainvision/__pycache__/brainvision.cpython-311.pyc,,
mne/io/brainvision/brainvision.py,sha256=1HCf20q26rrD2-Wt7kJ5PWFcRgWJ1lKJXSdhCWWFRcE,41878
mne/io/brainvision/tests/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
mne/io/brainvision/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/brainvision/tests/__pycache__/test_brainvision.cpython-311.pyc,,
mne/io/brainvision/tests/test_brainvision.py,sha256=m6LP3R-a5AFQwz2rnophO9KME_ZrexQ9px3NQaO4tSo,31664
mne/io/bti/__init__.py,sha256=YlhJeK83FN1jDmfZiSgXmsScTxAF-n3y0i7E3eSW2m8,127
mne/io/bti/__pycache__/__init__.cpython-311.pyc,,
mne/io/bti/__pycache__/bti.cpython-311.pyc,,
mne/io/bti/__pycache__/constants.cpython-311.pyc,,
mne/io/bti/__pycache__/read.cpython-311.pyc,,
mne/io/bti/bti.py,sha256=yzzIFN0F0tug74eZR7wYs1sxmt6w4cdYre5V_E_UNOk,53267
mne/io/bti/constants.py,sha256=_gVMPjpKrt5YrejvXE5NGCVBsptvsQxN8iyoER_twyM,2323
mne/io/bti/read.py,sha256=0iwqqjJVkT8lS4f53F3ZQINE3OpcimaIJY_VVJJ6ao0,2878
mne/io/bti/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/bti/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/bti/tests/__pycache__/test_bti.cpython-311.pyc,,
mne/io/bti/tests/test_bti.py,sha256=6-oBANOtyzljQf_GPfRGWoeTi_qjb8cXdaO1J-KVCr4,17212
mne/io/cnt/__init__.py,sha256=Jq-geUdSYB4Jy3dY4USIq_UkB07uftT-Ko_jTw1iRHM,54
mne/io/cnt/__pycache__/__init__.cpython-311.pyc,,
mne/io/cnt/__pycache__/_utils.cpython-311.pyc,,
mne/io/cnt/__pycache__/cnt.cpython-311.pyc,,
mne/io/cnt/_utils.py,sha256=yNBu5wLSvHqHv0JGSKeG2nq7GArmbHWCXej2z2GLrUk,4767
mne/io/cnt/cnt.py,sha256=v0SoVAcO2L1ZN9B6zsi-eKhGJOJHWphpJUk7wS3dfeQ,22458
mne/io/cnt/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/cnt/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/cnt/tests/__pycache__/test_cnt.cpython-311.pyc,,
mne/io/cnt/tests/test_cnt.py,sha256=eWEUeFbaQwvKEvH2zlgLipPlvXAMre0YSxhePUC7K3g,1973
mne/io/compensator.py,sha256=CaafwkMLtPv1oXn3iMcFgLBznxTMeYKry8Unis781Ck,5667
mne/io/constants.py,sha256=-IoGsZoOB2LBSg5vIFKNy_EALVOrGhR1tfiMmKkDnGg,40325
mne/io/ctf/__init__.py,sha256=v64OKvwrR5wdQseosrFSBBOiFwzk0YvGDVlBrIQkJi0,154
mne/io/ctf/__pycache__/__init__.cpython-311.pyc,,
mne/io/ctf/__pycache__/constants.cpython-311.pyc,,
mne/io/ctf/__pycache__/ctf.cpython-311.pyc,,
mne/io/ctf/__pycache__/eeg.cpython-311.pyc,,
mne/io/ctf/__pycache__/hc.cpython-311.pyc,,
mne/io/ctf/__pycache__/info.cpython-311.pyc,,
mne/io/ctf/__pycache__/markers.cpython-311.pyc,,
mne/io/ctf/__pycache__/res4.cpython-311.pyc,,
mne/io/ctf/__pycache__/trans.cpython-311.pyc,,
mne/io/ctf/constants.py,sha256=PFcNWVd3mbEIakhrEAgXH5niWdCF9VCP0L1Lswfpf7Y,726
mne/io/ctf/ctf.py,sha256=96gR-2Tg1c_M3TgXISsGmnshXOXi-AgkHmxp0DumiOo,11026
mne/io/ctf/eeg.py,sha256=mBvjbaq1-3AuYMFoSQ9WQE84faD0uL0DaYQnts21Ryg,3781
mne/io/ctf/hc.py,sha256=2T2vIRkrA3PNU83QJYqxaDoDPPMHQzAUp5iFlkXlh8A,2492
mne/io/ctf/info.py,sha256=fe2NFlokXhdodQSZgWpF-gwE7bg4cZvsw4E74AbYtEI,20449
mne/io/ctf/markers.py,sha256=9VH5XP_FqD8LBlA0wcDX0w6l3eulZWyWi4MDLcmA9m8,2865
mne/io/ctf/res4.py,sha256=2uiehevrEDMPwAmzMbbm2JxMJfHUm2t3xeepF-Oc0_Y,7078
mne/io/ctf/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/ctf/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/ctf/tests/__pycache__/test_ctf.cpython-311.pyc,,
mne/io/ctf/tests/test_ctf.py,sha256=4UZhGQMBfaPLMf2XmQH-3vZoqAqVZY6pufv1-Bb1FRg,24782
mne/io/ctf/trans.py,sha256=L29mCD8PButIzyMZJL5Y02aC2C3W1A5tOVg_e-wzHwg,4778
mne/io/ctf_comp.py,sha256=ZuNkHaOEx-qHuuH7pbtHYUqkG6edAM-Z_8pMWXBT3sM,5884
mne/io/curry/__init__.py,sha256=MYVrVkixxnYxd8wAYS7tMAcs8SaVlItFunCrGs_oRiA,144
mne/io/curry/__pycache__/__init__.cpython-311.pyc,,
mne/io/curry/__pycache__/curry.cpython-311.pyc,,
mne/io/curry/curry.py,sha256=C2V1BAfElgjRuRZTmlJ5tqO_WkczucqkhaaI4yZ64bU,21398
mne/io/curry/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/curry/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/curry/tests/__pycache__/test_curry.cpython-311.pyc,,
mne/io/curry/tests/test_curry.py,sha256=aIdV8mj4n0Tk4QqE5crDaI7tM0TYhu1-n6B4zHRn7o8,18899
mne/io/diff.py,sha256=jMcyveQjoiwZ3KUdxGmIq4WMmRH65yMjkZ6KbMP7PUM,1217
mne/io/edf/__init__.py,sha256=IBoHr4TZunNyLwElFamnDci3rUj-4UNoG9OpiqE5xhQ,177
mne/io/edf/__pycache__/__init__.cpython-311.pyc,,
mne/io/edf/__pycache__/edf.cpython-311.pyc,,
mne/io/edf/edf.py,sha256=OnTnSDQIsIj8iMHPNG4HOejeUZdKzsE9aRggwcz9Oz0,77065
mne/io/edf/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/edf/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/edf/tests/__pycache__/test_edf.cpython-311.pyc,,
mne/io/edf/tests/__pycache__/test_gdf.cpython-311.pyc,,
mne/io/edf/tests/test_edf.py,sha256=l0stNXhynoHY3JCx8GTw8GCB2VZLkIxR8rW_uNnNapg,31327
mne/io/edf/tests/test_gdf.py,sha256=9YZRt8diRPRd6KhA96N3ri-vW7j0_YCeJNSx3ks16pE,6075
mne/io/eeglab/__init__.py,sha256=NOIyEJTEUYdgBMODao8e9GEF6n2SFh-5HuGNzDg4bdM,156
mne/io/eeglab/__pycache__/__init__.cpython-311.pyc,,
mne/io/eeglab/__pycache__/_eeglab.cpython-311.pyc,,
mne/io/eeglab/__pycache__/eeglab.cpython-311.pyc,,
mne/io/eeglab/_eeglab.py,sha256=EGm92rJ_LoRCrSxtjRnqZ5XUySyDcySCeVnOE7ewR6k,2496
mne/io/eeglab/eeglab.py,sha256=GciRU6lAxXAHz2aEN5QddlPhedNyNaykDwrZi3w61L8,28018
mne/io/eeglab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/eeglab/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/eeglab/tests/__pycache__/test_eeglab.cpython-311.pyc,,
mne/io/eeglab/tests/test_eeglab.py,sha256=zX3nBolTNzM3chcz08_rx9S1CBb2SUF_IVsf9ra9HXs,22820
mne/io/egi/__init__.py,sha256=4S9YdvF8pnKGVNW3e42AJu3W7tHgVKI1mLeGOYOZqew,164
mne/io/egi/__pycache__/__init__.cpython-311.pyc,,
mne/io/egi/__pycache__/egi.cpython-311.pyc,,
mne/io/egi/__pycache__/egimff.cpython-311.pyc,,
mne/io/egi/__pycache__/events.cpython-311.pyc,,
mne/io/egi/__pycache__/general.cpython-311.pyc,,
mne/io/egi/egi.py,sha256=3qG23f3jwIREnWcx8hvYp-KvTTUgYH528na17JFEc18,11968
mne/io/egi/egimff.py,sha256=Itflyw4WKkViyuZgae-VM_mOq91SQ1p7hFQdEwTE8Jc,39718
mne/io/egi/events.py,sha256=HtNo3yl4lNMna985_kRJvkU09upIDj0E9gd4Hw6I7IA,4726
mne/io/egi/general.py,sha256=gcN7EzGCHa6NM9VSLpdWHu-1TL2La_X9tvkNzJU2iYE,6230
mne/io/egi/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/egi/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/egi/tests/__pycache__/test_egi.cpython-311.pyc,,
mne/io/egi/tests/test_egi.py,sha256=vse-NC1a8LPj05XNjyVNx6xZJMLC9WPXIfUC5c8IsXU,20107
mne/io/eximia/__init__.py,sha256=10tN1KzC58BkzeN3kCByRfT9Kv44P10fUnQQslRUBh0,155
mne/io/eximia/__pycache__/__init__.cpython-311.pyc,,
mne/io/eximia/__pycache__/eximia.cpython-311.pyc,,
mne/io/eximia/eximia.py,sha256=x7s6qL4zlexDzgC6XUjOz7KVxlEc5vIP-p7JCoE5y0Q,3185
mne/io/eximia/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/eximia/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/eximia/tests/__pycache__/test_eximia.cpython-311.pyc,,
mne/io/eximia/tests/test_eximia.py,sha256=64uzRhYOyBi5ArFQdguPn_YEy8EuXAcWarrqpgjipxQ,1622
mne/io/eyelink/__init__.py,sha256=BIrGIlw3-zef87RjLrLbIwy3ye1HU5hhS14cM066OgY,197
mne/io/eyelink/__pycache__/__init__.cpython-311.pyc,,
mne/io/eyelink/__pycache__/_utils.cpython-311.pyc,,
mne/io/eyelink/__pycache__/eyelink.cpython-311.pyc,,
mne/io/eyelink/_utils.py,sha256=A_76evbGWBrMcJ2orV9SnaLvHjeg3uKL2O8Z-KINIXU,9834
mne/io/eyelink/eyelink.py,sha256=AkQ0mljGhn4zo4k1E768UwkokeIkoWT-LiEWuxgjGPc,29825
mne/io/eyelink/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/eyelink/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/eyelink/tests/__pycache__/test_eyelink.cpython-311.pyc,,
mne/io/eyelink/tests/test_eyelink.py,sha256=kJ2470HWfOBobgF1uAO-tLzKH4tPg-y3bPuOmAQoDwI,10250
mne/io/fieldtrip/__init__.py,sha256=v-YgmFtm20bou5LnmXfqLfm70Xl0oQmAqX6Q1votDBA,247
mne/io/fieldtrip/__pycache__/__init__.cpython-311.pyc,,
mne/io/fieldtrip/__pycache__/fieldtrip.cpython-311.pyc,,
mne/io/fieldtrip/__pycache__/utils.cpython-311.pyc,,
mne/io/fieldtrip/fieldtrip.py,sha256=LLMvrWYrniJz8mLQMq3spXCtdS9t3PFVY6_rZvI14E0,6673
mne/io/fieldtrip/tests/__init__.py,sha256=N4MvMxgWpWYDOYsl3qR4dWQDEeyouTX78Srd2zFWnhA,158
mne/io/fieldtrip/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/fieldtrip/tests/__pycache__/helpers.cpython-311.pyc,,
mne/io/fieldtrip/tests/__pycache__/test_fieldtrip.cpython-311.pyc,,
mne/io/fieldtrip/tests/helpers.py,sha256=RneUtvrE0q7ACcyJFSRZn4Z0i-w7XckO4SogccLLWD0,6446
mne/io/fieldtrip/tests/test_fieldtrip.py,sha256=IceIZrZvYOl_aubc3qY0Rimx6UFLQr2ITlyfbzbrBAg,12151
mne/io/fieldtrip/utils.py,sha256=y3mEExidJek25n-TJUBxx5eSEmN6SqrjLpYRKuHB7AE,12204
mne/io/fiff/__init__.py,sha256=3Gpc37lZspGtbZ-Rlhq7A7LRrRY3KQKYEpJUg-fb9QY,79
mne/io/fiff/__pycache__/__init__.cpython-311.pyc,,
mne/io/fiff/__pycache__/raw.cpython-311.pyc,,
mne/io/fiff/raw.py,sha256=9I3hS4l1clftBpwIpdc5vTzM1u4BZKhnaq-PuOqEly4,20948
mne/io/fiff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/fiff/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/fiff/tests/__pycache__/test_raw_fiff.cpython-311.pyc,,
mne/io/fiff/tests/test_raw_fiff.py,sha256=VSvM0aq9kWPt94u8Kvf0vGRaeRrPlkP9-L1zn8t1WEE,77615
mne/io/fil/__init__.py,sha256=5TQmGafgxQykzxRy924Zd5ZbRYnq0R7Fi_CcyFeakvM,105
mne/io/fil/__pycache__/__init__.cpython-311.pyc,,
mne/io/fil/__pycache__/fil.cpython-311.pyc,,
mne/io/fil/__pycache__/sensors.cpython-311.pyc,,
mne/io/fil/fil.py,sha256=nAxU0u7VYDKQbk1m1FT0a-Kj9-oVAtPBpGWN8o-1VDw,10736
mne/io/fil/sensors.py,sha256=ZqZyThYCxzi6nVOaB-9M0IACRTmmMOhuNZbhFgx8D7I,4501
mne/io/fil/tests/__pycache__/test_fil.cpython-311.pyc,,
mne/io/fil/tests/test_fil.py,sha256=fJwmqs1bV3HkVKaUGm3h89OAXCE2E21LaEvZwuSYBLQ,4926
mne/io/hitachi/__init__.py,sha256=ySG34FhtQwPVpnWKMVdmYRJXaOc_iScll9Z5YrD2mhk,156
mne/io/hitachi/__pycache__/__init__.cpython-311.pyc,,
mne/io/hitachi/__pycache__/hitachi.cpython-311.pyc,,
mne/io/hitachi/hitachi.py,sha256=6cNs-Zi3A9MlnOWxN9GdqnAbJwCwPeOcl1XKCi6iRek,11767
mne/io/hitachi/tests/__pycache__/test_hitachi.cpython-311.pyc,,
mne/io/hitachi/tests/test_hitachi.py,sha256=PMaEvDnzvTLJfhBUlzDTV4QHX_ahuAhQ2ZlgrWz34bk,59466
mne/io/kit/__init__.py,sha256=cXmf0YXxk10Lofp4m55gEDewHF7wX5ORP0sKvP-Vvks,188
mne/io/kit/__pycache__/__init__.cpython-311.pyc,,
mne/io/kit/__pycache__/constants.cpython-311.pyc,,
mne/io/kit/__pycache__/coreg.cpython-311.pyc,,
mne/io/kit/__pycache__/kit.cpython-311.pyc,,
mne/io/kit/constants.py,sha256=dwbZNznvX-m_ahmq5SbhyFpmDC1AHTuS60rnKKmU6HA,9156
mne/io/kit/coreg.py,sha256=46bzOANsp3dNNgplXvmvKBXgUq1AH5oh1ayEv6Quyr4,7774
mne/io/kit/kit.py,sha256=15piSRNYJ661tkqmKcLSAhWhv8xCwoX7BYTrXtdJSoY,36729
mne/io/kit/tests/__init__.py,sha256=KQx-Qv5Rw-CHDuoa6mJCCLs3adJbFupab3ECO_ps1Ik,68
mne/io/kit/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/kit/tests/__pycache__/test_coreg.cpython-311.pyc,,
mne/io/kit/tests/__pycache__/test_kit.cpython-311.pyc,,
mne/io/kit/tests/test_coreg.py,sha256=oE2vFcjB0TXPyVQbDraEfm46fISRGD0W8XNiQs3xnT8,984
mne/io/kit/tests/test_kit.py,sha256=YU_3VJC1LHNyX0Bf1ySsPV2GMSQAt4Rt62soMnoaOCQ,16867
mne/io/matrix.py,sha256=btg3Um1ZEuhdtc9R8pAQ1F5gKibB83_KQXEEMqS5Pok,4398
mne/io/meas_info.py,sha256=ftsHtFikYgHrUYjEDfYF5JUSCGfnwb4_Y-rh76Ui2uQ,129762
mne/io/nedf/__init__.py,sha256=ZMWw2BXrv6uPuOludoalDon_QSlb6a8h6dZ-O1MB98A,162
mne/io/nedf/__pycache__/__init__.cpython-311.pyc,,
mne/io/nedf/__pycache__/nedf.cpython-311.pyc,,
mne/io/nedf/nedf.py,sha256=B0Qu-rT1VUYcUOxr6_8_vNnWrd1IBJd0bHQDy5feB1I,7783
mne/io/nedf/tests/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
mne/io/nedf/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/nedf/tests/__pycache__/test_nedf.cpython-311.pyc,,
mne/io/nedf/tests/test_nedf.py,sha256=jCgwkQgyk9LoYG8Eu72if510yZIdGrWs4wCP90xmN34,4467
mne/io/nicolet/__init__.py,sha256=1e2D_ewCsHWhsFOL4sYPVIEglclrRLzcaSmg9VimcIo,165
mne/io/nicolet/__pycache__/__init__.cpython-311.pyc,,
mne/io/nicolet/__pycache__/nicolet.cpython-311.pyc,,
mne/io/nicolet/nicolet.py,sha256=4SE4MLR-pMylwrbjp7whYg16EcaKrSPAPx-8SnInDjU,6568
mne/io/nicolet/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/nicolet/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/nicolet/tests/__pycache__/test_nicolet.cpython-311.pyc,,
mne/io/nicolet/tests/test_nicolet.py,sha256=YhUowA_xR0nDCjHcDzNHWksnjfgZ2ilhH2RzLF6Vdh8,750
mne/io/nihon/__init__.py,sha256=ysRRFw1PVOBQH7nxGuzipkgltCYP0QtmwXitlnnS4BM,160
mne/io/nihon/__pycache__/__init__.cpython-311.pyc,,
mne/io/nihon/__pycache__/nihon.cpython-311.pyc,,
mne/io/nihon/nihon.py,sha256=CLmd6Uu06dChT4Op2hn-x8Fae4OCSgO3QSQU0W4YfW4,18014
mne/io/nihon/tests/__pycache__/test_nihon.cpython-311.pyc,,
mne/io/nihon/tests/test_nihon.py,sha256=5D4x1J3YAElWlKb17klvq-88-vnxI2_jg_jphQcpj5A,3453
mne/io/nirx/__init__.py,sha256=-fO7Xeq0rpQgorFhG0dCJypuNzux3_47NrKXlfPtxX8,146
mne/io/nirx/__pycache__/__init__.cpython-311.pyc,,
mne/io/nirx/__pycache__/_localized_abbr.cpython-311.pyc,,
mne/io/nirx/__pycache__/nirx.cpython-311.pyc,,
mne/io/nirx/_localized_abbr.py,sha256=_XK9PtU5JcwS1rnnx18b41eigq4byRtyiX6zJpPoZKM,3950
mne/io/nirx/nirx.py,sha256=DkHJyjQW8rDpXnzwOWae1a48yJWUCwLBkDNZN8wHvMQ,22266
mne/io/nirx/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/nirx/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/nirx/tests/__pycache__/test_nirx.cpython-311.pyc,,
mne/io/nirx/tests/test_nirx.py,sha256=agzBuEcZCHK7ofhKFkNJHx1HUlHv1WgRrzD1VuYYEts,26253
mne/io/nsx/__init__.py,sha256=iHxMpyExBvkKHDDqNUJ9dzgY0NjVkOXT1kWh8PVT-sI,161
mne/io/nsx/__pycache__/__init__.cpython-311.pyc,,
mne/io/nsx/__pycache__/nsx.cpython-311.pyc,,
mne/io/nsx/nsx.py,sha256=AjVK3l1FFGfEBPcsMDHBvjmMbVFfF6cgzPOKA9wMnVM,19067
mne/io/nsx/tests/__pycache__/test_nsx.cpython-311.pyc,,
mne/io/nsx/tests/test_nsx.py,sha256=tXk7H43KfrJtaAGkHpUPPMMgCWxi5AyIYAXRO0IJcGM,10571
mne/io/open.py,sha256=C-nPMlY2F7paULF7-JvZeICDYK6H-UaiRWB_5Q8hkvI,11952
mne/io/persyst/__init__.py,sha256=CpJs7M1FrpdXz8gD0boDElp_yCqIPDbQaLhvGmYsCcM,149
mne/io/persyst/__pycache__/__init__.cpython-311.pyc,,
mne/io/persyst/__pycache__/persyst.cpython-311.pyc,,
mne/io/persyst/persyst.py,sha256=9XT6QN8xg1TjJCCarOIZuh-H4mWRmACLGVdDmD4IjKw,16850
mne/io/persyst/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/persyst/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/persyst/tests/__pycache__/test_persyst.cpython-311.pyc,,
mne/io/persyst/tests/test_persyst.py,sha256=4O38b3fcP_9NkhbQ-NZXG7x1N500bLVVQxEiB0P-DPA,8750
mne/io/pick.py,sha256=Ia6BS3KxRMBk8CWjk9QYdhRzgihqdcsNtFAC3lOsbIk,44660
mne/io/proc_history.py,sha256=4IGh4qbXBHPtfatj91hn85Gr2rTREVlqDk2JjuhVtRA,11702
mne/io/proj.py,sha256=_IR2GDadUC0yBQ2_m-TfQ1UnkEx6L_kC_OLx-SUwBNM,38787
mne/io/reference.py,sha256=ewQIZdBRJ3RadvkVJjW3dr8z4w7q6CFVvGlv6_KY7O8,24176
mne/io/snirf/__init__.py,sha256=d8rN4rWFnJlNFo2rBWNOq2KbSaq5AD-U9KaxItfJ8L0,149
mne/io/snirf/__pycache__/__init__.cpython-311.pyc,,
mne/io/snirf/__pycache__/_snirf.cpython-311.pyc,,
mne/io/snirf/_snirf.py,sha256=mOzaNTPi2lz5T5jhrs8dv78RDFmBvgLfmwytl7iQnXc,22474
mne/io/snirf/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/snirf/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/snirf/tests/__pycache__/test_snirf.cpython-311.pyc,,
mne/io/snirf/tests/test_snirf.py,sha256=n93zB-dW6WmRCgBMM514uO21N6Z30cRDh37md5pOslM,15517
mne/io/tag.py,sha256=om-IUaeq1BRyEeP96fC5Udv0i2rx-uSR7XvF_f66bT8,18642
mne/io/tests/__init__.py,sha256=hxLfxCq3A3k7ihUokXbiaA27XXdp5kHGGKWpTdUAqVc,71
mne/io/tests/__pycache__/__init__.cpython-311.pyc,,
mne/io/tests/__pycache__/test_apply_function.cpython-311.pyc,,
mne/io/tests/__pycache__/test_compensator.cpython-311.pyc,,
mne/io/tests/__pycache__/test_constants.cpython-311.pyc,,
mne/io/tests/__pycache__/test_meas_info.cpython-311.pyc,,
mne/io/tests/__pycache__/test_pick.cpython-311.pyc,,
mne/io/tests/__pycache__/test_proc_history.cpython-311.pyc,,
mne/io/tests/__pycache__/test_raw.cpython-311.pyc,,
mne/io/tests/__pycache__/test_read_raw.cpython-311.pyc,,
mne/io/tests/__pycache__/test_reference.cpython-311.pyc,,
mne/io/tests/__pycache__/test_show_fiff.cpython-311.pyc,,
mne/io/tests/__pycache__/test_utils.cpython-311.pyc,,
mne/io/tests/__pycache__/test_what.cpython-311.pyc,,
mne/io/tests/__pycache__/test_write.cpython-311.pyc,,
mne/io/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/io/tests/data/__pycache__/__init__.cpython-311.pyc,,
mne/io/tests/test_apply_function.py,sha256=rC7vQ2g9-O9IKkf6oD3h4BoXNXgDxSFB14esO3ui868,1892
mne/io/tests/test_compensator.py,sha256=qp5tZp0yf6328ZxKrgJH-Toj1gs5UCwnPEtV9ll1qSc,4249
mne/io/tests/test_constants.py,sha256=ziU4TRz0yNLDOp3GAooPmX8WkwKhU1nqSfXolHa4IVY,15218
mne/io/tests/test_meas_info.py,sha256=5BncuLEc4xCU3OMUxk8_NUoXA6rQiJ5nbVfA_PPURFM,40085
mne/io/tests/test_pick.py,sha256=ypDrpT7eSVEpBjr7jqTrxEB_iQWTS0xW5S4Z4gn9Ua0,28743
mne/io/tests/test_proc_history.py,sha256=eUzYLGqcEVsTbZg3BoAKBRmc_mLHOEdZLRASua0c1ek,1403
mne/io/tests/test_raw.py,sha256=yFMxfvCOODL_u586tsEuU1YrVrttc0LyHDCM-OnQYbQ,37691
mne/io/tests/test_read_raw.py,sha256=CnKvGH1zCg_zaeF6L8BgzuvvIq7bBWaGY3AXQFjIteY,2891
mne/io/tests/test_reference.py,sha256=dz5AMRwKoN4drIrGkvQTK1RDjBUjS3x3LwW7e88neTg,32089
mne/io/tests/test_show_fiff.py,sha256=6TBRN9wIU4TDJ_IEVk0MFlrbGpsfHzdpTC5OYcgRpOY,917
mne/io/tests/test_utils.py,sha256=FCx1BFFEBkUafU-Jf9tJI_LvW5mC2Knb3UJLiImos20,578
mne/io/tests/test_what.py,sha256=ktD1wUVovfzV5sCw9cabt9HlmPH30KxfRKbqWPZUDKo,1719
mne/io/tests/test_write.py,sha256=mjyxpocns7_hhCPLzMJqg2V9gQeksPtKroORHlMfgEc,782
mne/io/tree.py,sha256=Peau6VmFfRvKxiFw5GDwTAI93sEhcRFyLXrcKDMlpqg,4593
mne/io/utils.py,sha256=J5UwTlCn2qJ5OFm6yB0VvJ_Xsar5oM6bWyeYAt0XV6s,11608
mne/io/what.py,sha256=oVRBoZNg7Exi_LnEGxMMt1u31THPa8hNA2ENTzxXkPc,2128
mne/io/write.py,sha256=TvdVJpD05boRiuReMyip4s_cc2Dz1XG3M9IrIS3McVw,15702
mne/label.py,sha256=aiI_BOC01BxihZcrlrOujHilencwOB6RAvffdv7zwJw,100867
mne/minimum_norm/__init__.py,sha256=Cb6zIKwUgAScKTmRfcrEQCNgHtM-Idb3j9yJlMZu3Gc,724
mne/minimum_norm/__pycache__/__init__.cpython-311.pyc,,
mne/minimum_norm/__pycache__/_eloreta.cpython-311.pyc,,
mne/minimum_norm/__pycache__/inverse.cpython-311.pyc,,
mne/minimum_norm/__pycache__/resolution_matrix.cpython-311.pyc,,
mne/minimum_norm/__pycache__/spatial_resolution.cpython-311.pyc,,
mne/minimum_norm/__pycache__/time_frequency.cpython-311.pyc,,
mne/minimum_norm/_eloreta.py,sha256=tz0JLnnLCGNCeEbon_rXAwT-SuaVGtqbFIVwmlDWM-o,7301
mne/minimum_norm/inverse.py,sha256=jsUPXtFu_w6oPof_-BMFuHwYTqu5y-o32t5t9bkWpKE,72953
mne/minimum_norm/resolution_matrix.py,sha256=aE9034aAEStfTDHP-LPNrZKoFH9Ciyiw-9us_0ipXnQ,16458
mne/minimum_norm/spatial_resolution.py,sha256=LVewmAL95MLzlLqer3REXHvZQFfPih5_Bt1mNUsTNHo,12826
mne/minimum_norm/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/minimum_norm/tests/__pycache__/__init__.cpython-311.pyc,,
mne/minimum_norm/tests/__pycache__/test_inverse.cpython-311.pyc,,
mne/minimum_norm/tests/__pycache__/test_resolution_matrix.cpython-311.pyc,,
mne/minimum_norm/tests/__pycache__/test_resolution_metrics.cpython-311.pyc,,
mne/minimum_norm/tests/__pycache__/test_snr.cpython-311.pyc,,
mne/minimum_norm/tests/__pycache__/test_time_frequency.cpython-311.pyc,,
mne/minimum_norm/tests/test_inverse.py,sha256=A4SSr0KDXU64DH4B5ze-fW2K1FuXBgQdI1UmjR2bNtc,62883
mne/minimum_norm/tests/test_resolution_matrix.py,sha256=zv4_-ZbQ14FfhHfn6k15EFRgV4gtrxnl7zgTNmtmDKU,9806
mne/minimum_norm/tests/test_resolution_metrics.py,sha256=yk18KWsQhsbVWd8_mL_SqnZg3jV7pC-9Mjz_Pj8C93A,5905
mne/minimum_norm/tests/test_snr.py,sha256=aenv0YzKFnDEj4ibFeO5zR7i3oVW-3ihlQySpyQVLQc,1514
mne/minimum_norm/tests/test_time_frequency.py,sha256=ys95VECePS0COENHqDkY0wf2njsIsyh_g5Sg5tptHC4,8495
mne/minimum_norm/time_frequency.py,sha256=enikkydtH75H4LMHZlQA7-KkODoa10ZFkNJCQBhZ_os,30786
mne/misc.py,sha256=p8_EafMHPW85ywPBtzcyhwwnO0jY0ksRCl-AzpJLwRI,2926
mne/morph.py,sha256=rweJ0jaL13oI4msPnr-hKUHkWJ9oeXlou6Ev94CwO48,58424
mne/morph_map.py,sha256=vjbp-14-x8_q6fxvq-h8w3BRjuHYBEVU4sP3BjupCK8,9169
mne/parallel.py,sha256=a8ekT5Nsn5oh31IGQQHQfpymjPzWU3KX9fyEoqfcUCs,5069
mne/preprocessing/__init__.py,sha256=eA5WYLBUl2O19N3EP7bxC-X1UkPzYLsgvF1Kvm3pYQo,1716
mne/preprocessing/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/__pycache__/_csd.cpython-311.pyc,,
mne/preprocessing/__pycache__/_css.cpython-311.pyc,,
mne/preprocessing/__pycache__/_fine_cal.cpython-311.pyc,,
mne/preprocessing/__pycache__/_peak_finder.cpython-311.pyc,,
mne/preprocessing/__pycache__/_regress.cpython-311.pyc,,
mne/preprocessing/__pycache__/annotate_amplitude.cpython-311.pyc,,
mne/preprocessing/__pycache__/annotate_nan.cpython-311.pyc,,
mne/preprocessing/__pycache__/artifact_detection.cpython-311.pyc,,
mne/preprocessing/__pycache__/bads.cpython-311.pyc,,
mne/preprocessing/__pycache__/ctps_.cpython-311.pyc,,
mne/preprocessing/__pycache__/ecg.cpython-311.pyc,,
mne/preprocessing/__pycache__/eog.cpython-311.pyc,,
mne/preprocessing/__pycache__/hfc.cpython-311.pyc,,
mne/preprocessing/__pycache__/ica.cpython-311.pyc,,
mne/preprocessing/__pycache__/infomax_.cpython-311.pyc,,
mne/preprocessing/__pycache__/interpolate.cpython-311.pyc,,
mne/preprocessing/__pycache__/maxfilter.cpython-311.pyc,,
mne/preprocessing/__pycache__/maxwell.cpython-311.pyc,,
mne/preprocessing/__pycache__/otp.cpython-311.pyc,,
mne/preprocessing/__pycache__/realign.cpython-311.pyc,,
mne/preprocessing/__pycache__/ssp.cpython-311.pyc,,
mne/preprocessing/__pycache__/stim.cpython-311.pyc,,
mne/preprocessing/__pycache__/xdawn.cpython-311.pyc,,
mne/preprocessing/_csd.py,sha256=Mxj9w7rE9-cdnOJ_H4vU49Q9Ea6UDXhIH4WJkbXECa0,11302
mne/preprocessing/_css.py,sha256=qYimUs5TldV-I2ILJAuiCzPa5bm7IPqMVFSZi6ljeXs,2963
mne/preprocessing/_fine_cal.py,sha256=2_ERk0owRY5Gg1VTInVRrPWTxoEqpt25g2G2FvSP4IE,20851
mne/preprocessing/_peak_finder.py,sha256=7xocmtHv4U0ews0dnSLPbI_VrEi5u40CWpuV12PXy1I,6277
mne/preprocessing/_regress.py,sha256=cRbmuFvgPHuWlEdO4u2C9I1A_D8rjVYxC_DekzdsgNE,13215
mne/preprocessing/annotate_amplitude.py,sha256=hPMXmgXx2Ke9cZPZrl-tN-aJZpyfwti0igZ1RIZ8Wcs,11365
mne/preprocessing/annotate_nan.py,sha256=90v2Roe7_lgbWR57qilft3EUMTUJm4rEfO1UehSGdCI,1139
mne/preprocessing/artifact_detection.py,sha256=pjIdZj5IZG7UIwO3vuYhFMj8b83EH3m0y3AlJsu-isg,22724
mne/preprocessing/bads.py,sha256=9Iu_xHMYjWG70WwxX8zaMrGUDZvdaZdKVeGZBCE47IM,1549
mne/preprocessing/ctps_.py,sha256=iZCnOa7jWJ5ybXr9a_8fbn54MmcdDeDUuKyTbc4jzHM,5139
mne/preprocessing/ecg.py,sha256=Fv_V9O0agd1pfRa5dpeqKkDpBjr6pupOJY4jzXk8jgQ,15571
mne/preprocessing/eog.py,sha256=6p_TmDTjgygR1QsbZ3dOct0EQQzfLIVf-c6UXEC-oh8,9849
mne/preprocessing/eyetracking/__init__.py,sha256=B3MAlJXSMxm2iaVb-QcT8qd8RpMY4xEPUjPnZpWVvRs,295
mne/preprocessing/eyetracking/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/eyetracking/__pycache__/_pupillometry.cpython-311.pyc,,
mne/preprocessing/eyetracking/__pycache__/calibration.cpython-311.pyc,,
mne/preprocessing/eyetracking/__pycache__/eyetracking.cpython-311.pyc,,
mne/preprocessing/eyetracking/_pupillometry.py,sha256=qYAIc7V0YKUDZ5GwcLE-c432J-paXOuJfMXxNjSAOZc,4511
mne/preprocessing/eyetracking/calibration.py,sha256=QaXPMa_3AUeu56pUpalzBlckwLU2ngYsxjjYv8U_pkI,8093
mne/preprocessing/eyetracking/eyetracking.py,sha256=pZisDenYuuqBGqaZZx8Sz6IaiDgAeEr2t7mTvGKbDJ0,5515
mne/preprocessing/eyetracking/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/preprocessing/eyetracking/tests/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/eyetracking/tests/__pycache__/test_calibration.cpython-311.pyc,,
mne/preprocessing/eyetracking/tests/__pycache__/test_pupillometry.cpython-311.pyc,,
mne/preprocessing/eyetracking/tests/test_calibration.py,sha256=kBRpNAt5kJ2pDtqYhmgDyHpyz-cOjTDKj6-jlhrIwS8,7047
mne/preprocessing/eyetracking/tests/test_pupillometry.py,sha256=fReSU0BQncirAvKrUnTzHa7wXZOZu5jJ-6lD8Np8zUc,2633
mne/preprocessing/hfc.py,sha256=CebjJVGJiSZmDKzHrVvKBYEVmt6rNaOw71m2jCfwyd4,3368
mne/preprocessing/ica.py,sha256=fqO3-Ai5ptp1y7iSUDe2Y4i9VNCJzg1yZce5gPe9zCI,128570
mne/preprocessing/ieeg/__init__.py,sha256=qjft65Cy9hPa9szpuoj-2GsDGA4qjGS0rfRcq03oBhg,242
mne/preprocessing/ieeg/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/ieeg/__pycache__/_projection.cpython-311.pyc,,
mne/preprocessing/ieeg/__pycache__/_volume.cpython-311.pyc,,
mne/preprocessing/ieeg/_projection.py,sha256=_G5puWwgcl9vH21KIpSCQcZ1Cm7KN4JiISRt7GbbmQA,7434
mne/preprocessing/ieeg/_volume.py,sha256=g-gNkD7MB8JJczAyO6mKJo45isSaIe1A3wfG6c6whhQ,8858
mne/preprocessing/ieeg/tests/__pycache__/test_projection.cpython-311.pyc,,
mne/preprocessing/ieeg/tests/__pycache__/test_volume.cpython-311.pyc,,
mne/preprocessing/ieeg/tests/test_projection.py,sha256=XChxBB7CRukhqrJa02U-cbABeGmLh01XA6DcEzBLtQk,7620
mne/preprocessing/ieeg/tests/test_volume.py,sha256=QMVipbmdXzGAMpZKbjeCzlnk_lhCzH8XZxTy4dSZVts,4713
mne/preprocessing/infomax_.py,sha256=-npP9IdBfL_oSrO6CZtgawQ-WOdLHeU3PMPkSCgkPd0,11695
mne/preprocessing/interpolate.py,sha256=q25MsrT5ZRbjwqbwPfP3a_2DS7TvcOXlOfpT_JbwVHw,8651
mne/preprocessing/maxfilter.py,sha256=xjg6yCDYs4rvK-cE0l_pfqW4ahhnyMevu0EbymEf50U,6448
mne/preprocessing/maxwell.py,sha256=G4O_Ui4r9rGtWeQKqUUtNAhWBf85xEcaLjOFyBAnrKE,111776
mne/preprocessing/nirs/__init__.py,sha256=M4LLqvn2FTFYqkVRszibprQPeb2DnnVVLiOXWtIHK2Y,711
mne/preprocessing/nirs/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/nirs/__pycache__/_beer_lambert_law.cpython-311.pyc,,
mne/preprocessing/nirs/__pycache__/_optical_density.cpython-311.pyc,,
mne/preprocessing/nirs/__pycache__/_scalp_coupling_index.cpython-311.pyc,,
mne/preprocessing/nirs/__pycache__/_tddr.cpython-311.pyc,,
mne/preprocessing/nirs/__pycache__/nirs.cpython-311.pyc,,
mne/preprocessing/nirs/_beer_lambert_law.py,sha256=vkUn812Kc8ulniOmv4MqYQADSlo-enuS1pLpbQhLuko,3697
mne/preprocessing/nirs/_optical_density.py,sha256=tjOEI0maGsK1nJRqjVBgc7x69GgnqS_JdnY4wWw2VLk,1719
mne/preprocessing/nirs/_scalp_coupling_index.py,sha256=7RQWDR9s1UvVd4fSQMtaKE_N2JTjetOijrPHNGRH9MI,1861
mne/preprocessing/nirs/_tddr.py,sha256=iEwh7Vy5FcxG6d_Wqi8bPPQOmQaAk9upAFc52OSa90I,5020
mne/preprocessing/nirs/nirs.py,sha256=RCXDeswZR-7GtUmWfd33OPWlsg_a9CCA5ciL0GC0034,11536
mne/preprocessing/nirs/tests/__pycache__/test_beer_lambert_law.cpython-311.pyc,,
mne/preprocessing/nirs/tests/__pycache__/test_nirs.cpython-311.pyc,,
mne/preprocessing/nirs/tests/__pycache__/test_optical_density.cpython-311.pyc,,
mne/preprocessing/nirs/tests/__pycache__/test_scalp_coupling_index.cpython-311.pyc,,
mne/preprocessing/nirs/tests/__pycache__/test_temporal_derivative_distribution_repair.cpython-311.pyc,,
mne/preprocessing/nirs/tests/test_beer_lambert_law.py,sha256=2TDcfVNISMbjC7umy0GJaPnnuUJlnw23rjvvyF1PZWo,3501
mne/preprocessing/nirs/tests/test_nirs.py,sha256=vBuVhd2gvmNYPCaxMC7sLcUFSuRTDqZUen7SwWFTSv4,20195
mne/preprocessing/nirs/tests/test_optical_density.py,sha256=wO181PpT_G-OyJP-rjceoq7xi_GT3cQGTMUf6DEm1oM,1991
mne/preprocessing/nirs/tests/test_scalp_coupling_index.py,sha256=ohsWliAs6TtUC-OKzlnvyUTlAHgT3waa-vXGDDUSI6A,2579
mne/preprocessing/nirs/tests/test_temporal_derivative_distribution_repair.py,sha256=ujSgwf5MAUbmSYUPJhusTNuhv8u1JvpA4Ovd6Gg88Nk,1989
mne/preprocessing/otp.py,sha256=x20tAGC4sc0hhd8Pi12Ubr3RB3BfOqtOXTwUfcjz85s,5033
mne/preprocessing/realign.py,sha256=A28YTSay5_GEB69EjDCuoumYWVVmQoid7QWZ6l3WzT4,4251
mne/preprocessing/ssp.py,sha256=0Ih4jMDwvlBkGfhGYqH9_WRxzv_xLj_zl8sYTzJjfz0,15699
mne/preprocessing/stim.py,sha256=2LvAmm9VWVRMaTIpPlhQHu1TO0BeZoPycA2k9LrDOnY,4383
mne/preprocessing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/preprocessing/tests/__pycache__/__init__.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_annotate_amplitude.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_annotate_nan.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_artifact_detection.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_csd.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_css.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_ctps.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_ecg.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_eeglab_infomax.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_eog.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_fine_cal.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_hfc.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_ica.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_infomax.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_interpolate.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_maxwell.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_otp.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_peak_finder.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_realign.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_regress.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_ssp.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_stim.cpython-311.pyc,,
mne/preprocessing/tests/__pycache__/test_xdawn.cpython-311.pyc,,
mne/preprocessing/tests/test_annotate_amplitude.py,sha256=tMjdGMGgacH7A1sfzRAIqeyXUXVCOH0sUz8Gq2qhnqw,16015
mne/preprocessing/tests/test_annotate_nan.py,sha256=oGEahmO8L_k4zjdcLDN62-7Fjo87xpo2-8MumLvqHZ4,1554
mne/preprocessing/tests/test_artifact_detection.py,sha256=IupY_gve1byFpUJWqoDLCrPO2ipTCyz179v1MzvOkls,9840
mne/preprocessing/tests/test_csd.py,sha256=hAWz96Cvr5aU0kVITfT_3l-0KOpA3nF9w1-2FlkTKWc,8921
mne/preprocessing/tests/test_css.py,sha256=Avg_Kz_p4LEhUyjMnAJZw4KB5sAeb7lCMWQ_q7imljk,1697
mne/preprocessing/tests/test_ctps.py,sha256=gJ3MZdaegNTBWL8R19WTqQTikUBlSgXNZznGrhJiMIc,2914
mne/preprocessing/tests/test_ecg.py,sha256=3jSc3P9K-Q0HWGX6NLZy0aVzvo9q6AOtdruWPhqd_fw,3481
mne/preprocessing/tests/test_eeglab_infomax.py,sha256=-pUfcnk9GXmwMRGBds_VkH984SYkcc8pkTZOg_vYE3s,6953
mne/preprocessing/tests/test_eog.py,sha256=OcOM5M2HIgpF0CIRzmGdLcGTziAaURT9KdBww4Gu0UI,1103
mne/preprocessing/tests/test_fine_cal.py,sha256=8QWpRkPvJGQs_wTqGUqMEXhmJb5dXzoWO4Cx0ZqRLwI,6236
mne/preprocessing/tests/test_hfc.py,sha256=d49SO4VxpJuvn2NgDiMrO7yvX2XpFkv2q-huBqaC_lE,5495
mne/preprocessing/tests/test_ica.py,sha256=t_Op0oKBYbNKwkx-vY8JiF6fOzE3Gokuq3_4nPzwwI8,61505
mne/preprocessing/tests/test_infomax.py,sha256=CAZNpID2kKROJO8KQFJLEScC3oMEEvh88xGpvnqUMA8,5961
mne/preprocessing/tests/test_interpolate.py,sha256=7K4eNn60DqS4MNTE7Ctid4yO_OHCCkWrfpN0hrOI-J8,7517
mne/preprocessing/tests/test_maxwell.py,sha256=ckfrGVj9Ch1qfgOe2e6GDE8Zc81_LZx1eOey0CR2_eM,71419
mne/preprocessing/tests/test_otp.py,sha256=__LZuBIXc5_IcRi4prKoGRE6eTls94A3ABgaE73Xn1k,3770
mne/preprocessing/tests/test_peak_finder.py,sha256=ofe4dFm-Yu7cHIU-1lGXtUNTdgnplGzjXfPSVYy97Hc,1168
mne/preprocessing/tests/test_realign.py,sha256=-kijMm14duXpXoNGXYsnCQjAZ9xpc0pqdxNvWce0ih4,5050
mne/preprocessing/tests/test_regress.py,sha256=_OloQsVpoq3HbGhIofdRrTei65R7JwEi0xnlC3lizIM,6831
mne/preprocessing/tests/test_ssp.py,sha256=I6gUB9cm6CT0icqwVM2B6cotsflA6OJ7NzMFKDip1kY,9007
mne/preprocessing/tests/test_stim.py,sha256=Cl_B9SgXXjkqKoI6pVf411lVVhFpZvA9VoADFAEqYQs,4074
mne/preprocessing/tests/test_xdawn.py,sha256=VojzAZ5ouQY5FPLyxFKSrW4VAoSi5jujoN67SxA9oVM,12788
mne/preprocessing/xdawn.py,sha256=SI1AdW4yXDxvNWEjDLBc1zoVZZ8gmYJX2DipX0dCuvA,24815
mne/proj.py,sha256=-Hj3rc81ur1xnZURgLo8GQQ812VTHtEs33wl3z7XArc,16185
mne/rank.py,sha256=JNybumyw-Q36mJom0YtizaXI_fLVDMEuFoN2tRS1ZMo,17318
mne/report/__init__.py,sha256=tqUcHj-UVPcyKY2y5rgEIEIzzzDA4SkgiaceqOZBnLs,56
mne/report/__pycache__/__init__.cpython-311.pyc,,
mne/report/__pycache__/report.cpython-311.pyc,,
mne/report/js_and_css/bootstrap-icons/__pycache__/gen_css_for_mne.cpython-311.pyc,,
mne/report/js_and_css/bootstrap-icons/bootstrap-icons.mne.min.css,sha256=L1WFBJVHDy40BDyvyCm__LNm3iJxnKbLEUuHrFJvUDA,186639
mne/report/js_and_css/bootstrap-icons/gen_css_for_mne.py,sha256=2Wis2SI5Cap7bPQUcRjnSl-A2ULJut3QdyLURkWsERQ,1483
mne/report/js_and_css/bootstrap-table/bootstrap-table-copy-rows.min.js,sha256=c5CfkkaulJ0KVySrsclFQIkGu8J_o_jFtYX0lLyMIQc,15127
mne/report/js_and_css/bootstrap-table/bootstrap-table-export.min.js,sha256=n5-lMFyA6JMA7scukYMCE3qBNFvgFCuZQQ2ZiyWa5gU,28095
mne/report/js_and_css/bootstrap-table/bootstrap-table.min.css,sha256=ZD9Tb2mCr01yh68j-JrUV91-SoL5xpUzChErCIHDCko,9246
mne/report/js_and_css/bootstrap-table/bootstrap-table.min.js,sha256=BqVxqb61C9Xd1gUXFZd13-wfPnmP_zj7EXriNQ3Jup0,121976
mne/report/js_and_css/bootstrap-table/tableExport.min.js,sha256=eOCV9Lq8lSWDvcYbY9uvMVnaI68CD-VG_cCkf63vZOo,77394
mne/report/js_and_css/bootstrap.bundle.min.js,sha256=5aErhPlUPVujIxg3wvJGdWNAWqZqWCtvxACYX4XfSa0,78871
mne/report/js_and_css/bootstrap.min.css,sha256=sAcc18zvMnaJZrNT4v8J0T4HqzEUiUTlVFgDIywjQek,162764
mne/report/js_and_css/highlightjs/atom-one-dark-reasonable.min.css,sha256=gaxVPXwTfyU3iMjSmLJa0lxhUNHjlKpB6RnEuWcGRhE,1193
mne/report/js_and_css/highlightjs/atom-one-light.min.css,sha256=WT7iR11CrJrnAaF3o1OCW9ZhtiSfoa1LrSMlQIJenoM,856
mne/report/js_and_css/highlightjs/default.min.css,sha256=CGf39SzxAmEe09Ojoh16_2xTRNleX9wBZcEXVnNqwSM,1147
mne/report/js_and_css/highlightjs/highlight.min.js,sha256=eODQKhUO3ZVpA5NiZ_yyCMae7R70bfvIKDObx62XxEo,132027
mne/report/js_and_css/jquery-3.6.0.min.js,sha256=_xUj-3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej_m4,89501
mne/report/js_and_css/report.js,sha256=cmmffH4786kP9xBraNaJYLkSQrxpYlOVFvkDXAyoDJ4,8008
mne/report/js_and_css/report.sass,sha256=pDP4UOQXklhboSZh6_BE32ucbI9GnZKo9axuDmvnxVA,257
mne/report/report.py,sha256=oFr1MvD8MPF5e7pqBydoE0emBlFNDef8mf3IZXrmlT0,142835
mne/report/tests/__pycache__/test_report.cpython-311.pyc,,
mne/report/tests/test_report.py,sha256=baj9O9CQduml9k2o3LbIxDzrrZWGKibTQ0IVuRAyNjk,38676
mne/simulation/__init__.py,sha256=b3oHwxPiTD-l30ANVKMv_wJD6vWOB6_lVuDo3_LHrTY,270
mne/simulation/__pycache__/__init__.cpython-311.pyc,,
mne/simulation/__pycache__/_metrics.cpython-311.pyc,,
mne/simulation/__pycache__/evoked.cpython-311.pyc,,
mne/simulation/__pycache__/raw.cpython-311.pyc,,
mne/simulation/__pycache__/source.cpython-311.pyc,,
mne/simulation/_metrics.py,sha256=BboTGP6uJS4a-PiYoUG2Zmla6rQ47eVPbX2fRYNYatI,419
mne/simulation/evoked.py,sha256=DL6EBwU5Hypi5HVcBC_O_Xh6YJqxGeo7Bih_rGV071c,5498
mne/simulation/metrics/__init__.py,sha256=cW9aQWpgeHLMICwd6aXjZDWfU3MWg9wHv5Rf2-FECdg,348
mne/simulation/metrics/__pycache__/__init__.cpython-311.pyc,,
mne/simulation/metrics/__pycache__/metrics.cpython-311.pyc,,
mne/simulation/metrics/metrics.py,sha256=k3YsI6lUTOJGnbySGgC0GOfyscmLJoTOacybNIV3ylo,17520
mne/simulation/metrics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/simulation/metrics/tests/__pycache__/__init__.cpython-311.pyc,,
mne/simulation/metrics/tests/__pycache__/test_metrics.cpython-311.pyc,,
mne/simulation/metrics/tests/test_metrics.py,sha256=KRkKHAsNpiDIOxlrVltxlAC4DPRCx2I0vxpZ8YV-e5Y,9417
mne/simulation/raw.py,sha256=Z4ENJXWxcEdxxkW4nc1-FppOBpVR8Lu6xo3_yDbWFfU,30788
mne/simulation/source.py,sha256=iJr6OsC3Up03c2qioqcho2s7llLZdlqnIeubkLE7BaI,21249
mne/simulation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/simulation/tests/__pycache__/__init__.cpython-311.pyc,,
mne/simulation/tests/__pycache__/test_evoked.cpython-311.pyc,,
mne/simulation/tests/__pycache__/test_metrics.cpython-311.pyc,,
mne/simulation/tests/__pycache__/test_raw.cpython-311.pyc,,
mne/simulation/tests/__pycache__/test_source.cpython-311.pyc,,
mne/simulation/tests/test_evoked.py,sha256=m2FhP7MqMtlPc15IRDePD_o0AGuz2cFkH3JbUNg0ai4,7263
mne/simulation/tests/test_metrics.py,sha256=2Hon86YKvZk7r6Yl23KBaUfAaz_56wRs0m4w0P_f9GE,1782
mne/simulation/tests/test_raw.py,sha256=_xve6bZLq0yFntpG3w5u4fRDh0ChG8WnPt0fM-oRYe4,22779
mne/simulation/tests/test_source.py,sha256=H7fmO9v1bJch2yk4B7XE5Rkz24y2CXB5nUqyQ1Whp7A,15089
mne/source_estimate.py,sha256=6dSwHLZIuPFYF5_p0axRWnqY5prON5FG5EZ2kRIExtE,131977
mne/source_space.py,sha256=6_bCHlefQhrLlSF6ABKTxb77qkf8Zktrz8wB_IXZV9c,121270
mne/stats/__init__.py,sha256=Q222ZOOP-aN3G-SdD88UMM6ER9HQgiKe2D4Za8a8CFM,662
mne/stats/__pycache__/__init__.cpython-311.pyc,,
mne/stats/__pycache__/_adjacency.cpython-311.pyc,,
mne/stats/__pycache__/cluster_level.cpython-311.pyc,,
mne/stats/__pycache__/multi_comp.cpython-311.pyc,,
mne/stats/__pycache__/parametric.cpython-311.pyc,,
mne/stats/__pycache__/permutations.cpython-311.pyc,,
mne/stats/__pycache__/regression.cpython-311.pyc,,
mne/stats/_adjacency.py,sha256=Sb67jHHUEkd81DWfjBpXjWog55fUgmdYK3M35En9xeg,4702
mne/stats/cluster_level.py,sha256=fa-1K-TV0ZoUN54QxfRztLfyiMINzwXsdz_uZKS-HFc,60183
mne/stats/multi_comp.py,sha256=ma7x4c92h5IX1zW_xZ9vZvfqwlisucuCN1m8GX8FHj8,2997
mne/stats/parametric.py,sha256=oNypAbgsF25cC_OnSKIYll3CFh9xj5qRdHMk17Mp54U,14482
mne/stats/permutations.py,sha256=HL7aG5v88uT-kSA_azC10XDUwcD_z5SnOLC9yp9PxCk,6035
mne/stats/regression.py,sha256=NAKk1NH5ztWd-q0SRMMLKhsUiKK4drHT4kGbgYMn2ag,17689
mne/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/stats/tests/__pycache__/__init__.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_adjacency.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_cluster_level.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_multi_comp.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_parametric.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_permutations.cpython-311.pyc,,
mne/stats/tests/__pycache__/test_regression.cpython-311.pyc,,
mne/stats/tests/test_adjacency.py,sha256=Orj6vQv4m5D2D_xhycJpwft1zu_RNhi6OsosDgWRSD0,1284
mne/stats/tests/test_cluster_level.py,sha256=Rg-5gs64ljt6hK1eLDMdxzdFJ5vPKGx83p4lsWUzZXE,30774
mne/stats/tests/test_multi_comp.py,sha256=BmoVK3mUS3oi3-er0NVoXRX_SfxT3jrTAMT40TH4nkE,1952
mne/stats/tests/test_parametric.py,sha256=nVoj5qVIM4DzfMOhH7fF5NQ0KDwa3vBQK5p7H9pR57o,5963
mne/stats/tests/test_permutations.py,sha256=FoPW6-6KTrM-hjTNGEyW8u2GyR5Zy0Pf94blbJbFhuo,3245
mne/stats/tests/test_regression.py,sha256=58v0sVr08NVcB9kOwimiU5cpcVgsGj3x4kFsozq3UY0,5544
mne/surface.py,sha256=kf10Dq5LMZ1UWZM5BcSsKpZDHnToqBfZo3sJR27NXLs,72603
mne/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/tests/__pycache__/__init__.cpython-311.pyc,,
mne/tests/__pycache__/test_annotations.cpython-311.pyc,,
mne/tests/__pycache__/test_bem.cpython-311.pyc,,
mne/tests/__pycache__/test_chpi.cpython-311.pyc,,
mne/tests/__pycache__/test_coreg.cpython-311.pyc,,
mne/tests/__pycache__/test_cov.cpython-311.pyc,,
mne/tests/__pycache__/test_defaults.cpython-311.pyc,,
mne/tests/__pycache__/test_dipole.cpython-311.pyc,,
mne/tests/__pycache__/test_docstring_parameters.cpython-311.pyc,,
mne/tests/__pycache__/test_epochs.cpython-311.pyc,,
mne/tests/__pycache__/test_event.cpython-311.pyc,,
mne/tests/__pycache__/test_evoked.cpython-311.pyc,,
mne/tests/__pycache__/test_filter.cpython-311.pyc,,
mne/tests/__pycache__/test_freesurfer.cpython-311.pyc,,
mne/tests/__pycache__/test_import_nesting.cpython-311.pyc,,
mne/tests/__pycache__/test_label.cpython-311.pyc,,
mne/tests/__pycache__/test_line_endings.cpython-311.pyc,,
mne/tests/__pycache__/test_misc.cpython-311.pyc,,
mne/tests/__pycache__/test_morph.cpython-311.pyc,,
mne/tests/__pycache__/test_morph_map.cpython-311.pyc,,
mne/tests/__pycache__/test_ola.cpython-311.pyc,,
mne/tests/__pycache__/test_parallel.cpython-311.pyc,,
mne/tests/__pycache__/test_proj.cpython-311.pyc,,
mne/tests/__pycache__/test_rank.cpython-311.pyc,,
mne/tests/__pycache__/test_read_vectorview_selection.cpython-311.pyc,,
mne/tests/__pycache__/test_source_estimate.cpython-311.pyc,,
mne/tests/__pycache__/test_source_space.cpython-311.pyc,,
mne/tests/__pycache__/test_surface.cpython-311.pyc,,
mne/tests/__pycache__/test_transforms.cpython-311.pyc,,
mne/tests/test_annotations.py,sha256=Cqi9AYpg5N5pkoD9V39_4TTFIiRRA-NQKjRiaUntnJU,65660
mne/tests/test_bem.py,sha256=17vhZ07ggTrnQPtbC7E0vo0oc-OFKAU4pVG_YIfPjbk,22891
mne/tests/test_chpi.py,sha256=xG1Z47Jp_UyLprXyKnSKHHX6k1OsuMi6J9EJ3B0B61k,32976
mne/tests/test_coreg.py,sha256=mHhZUuzEV4JCBN3PZxCAl-J47cgqT8uabvHY_scqQpw,22434
mne/tests/test_cov.py,sha256=zSAqbPy8SerZLiJ8uSCZTYgBXjCSwqZqomSBV-jQ4TY,35409
mne/tests/test_defaults.py,sha256=HfCP48Gk6jW_SU19tFr-7Sc3WyIRhBYOJjTp123MdBQ,1952
mne/tests/test_dipole.py,sha256=lBDpLglOqqddvN0uvG3imBJhLDYEYUilfXKwmI-oiKQ,21338
mne/tests/test_docstring_parameters.py,sha256=glheASAFOYSTUc2hgyeFm7090N1UX7NP-sSMFQRf7hU,11083
mne/tests/test_epochs.py,sha256=1OW2tfNFuXNUw-WgcGBQBi4L2gV-LOZS81E4Ft4p9as,171927
mne/tests/test_event.py,sha256=ZqqBhImxGz8zjg6uIYchos2emJs2n_CBZvPt5P1gigY,24045
mne/tests/test_evoked.py,sha256=puovh_IAN7JPpWw3ddeVNR7gBaLLrSCX7eWrI8uzp6Y,35988
mne/tests/test_filter.py,sha256=c55UlLZj2xBKe1xCaHUJQ3F8CwTBRZsbFSvvSDIlMY4,36094
mne/tests/test_freesurfer.py,sha256=sWUH-U_oFYpfQSP4HLrPxzRDkAZCFBR41z5pFmxlDjo,10356
mne/tests/test_import_nesting.py,sha256=eligUO2nwLEysIP40SARXG_z8dfYXnqawMVrJdU0NF4,1864
mne/tests/test_label.py,sha256=ugNTIpvWNcyZzI8dg8ulz18fALqy6gy-_JgFS2UZmeo,43402
mne/tests/test_line_endings.py,sha256=KQ4KiqeSLvFM9fugMfddYArEXR-xLpdZ_ocir4v7T1c,2778
mne/tests/test_misc.py,sha256=vEKGgqrAlkVVvSyrl8riX7-gpyCua7kznMdWK94U5g8,362
mne/tests/test_morph.py,sha256=jdZAwmbuTmMAHE4gsk_r1Tz0MpnYxyypi2cmBTKhtQk,43508
mne/tests/test_morph_map.py,sha256=wIvWqOZ-TCE3rU3uXwJ7LcF1V9-juNLjRYd9UrsX5mk,2165
mne/tests/test_ola.py,sha256=DfdwQpXZYYiyEfkXvF5eEILLJHCvmTDhDWQYPgK5Gbw,4672
mne/tests/test_parallel.py,sha256=gnWlJ9j1F5v9br1j5iMpIT55-9gtUOeoEHDEP7a6SOE,1282
mne/tests/test_proj.py,sha256=gK8YkDHqXSvF-PpMYWD4ncphFuHIOg8iNyENFGxFnyM,21664
mne/tests/test_rank.py,sha256=eOhJmCE8yauOSWc51LBi_LwKDngVowmQ9aVUGsRPlCk,11588
mne/tests/test_read_vectorview_selection.py,sha256=50FwZuTcfhZkNc0KopX49AsrUQiClEixzYHO3oo9rBs,1953
mne/tests/test_source_estimate.py,sha256=n70nuhxwA8meLuYTliAYoegz9fZOHrEdW7QrG5kexzc,76344
mne/tests/test_source_space.py,sha256=1BnxRX9o1OrVEy22SsvyWE_il-g-1vRfex0oF2k3O5Q,39370
mne/tests/test_surface.py,sha256=KiASl4l4dh35EwbzZitdtPgtFJLcYsNWqcFJ5LmwM_Q,14135
mne/tests/test_transforms.py,sha256=lsGgDfCQWE_0xICt6Yd8_dXtJKQbG139MXqX5jr_t5o,22045
mne/time_frequency/__init__.py,sha256=rQg6bKpbRRIR6oxr7Bm4RWnbuJzhyQOdE3azx7tECv8,746
mne/time_frequency/__pycache__/__init__.cpython-311.pyc,,
mne/time_frequency/__pycache__/_stft.cpython-311.pyc,,
mne/time_frequency/__pycache__/_stockwell.cpython-311.pyc,,
mne/time_frequency/__pycache__/ar.cpython-311.pyc,,
mne/time_frequency/__pycache__/csd.cpython-311.pyc,,
mne/time_frequency/__pycache__/multitaper.cpython-311.pyc,,
mne/time_frequency/__pycache__/psd.cpython-311.pyc,,
mne/time_frequency/__pycache__/spectrum.cpython-311.pyc,,
mne/time_frequency/__pycache__/tfr.cpython-311.pyc,,
mne/time_frequency/_stft.py,sha256=LnXz0LoDq4Y6q1qRB2qRQ4Zv2W0jLBOCD8t9NhKtUHc,6811
mne/time_frequency/_stockwell.py,sha256=zLVAw-U18wIajZHpQCnNwVJOqyVCES3uSZaB3_BzMxs,9665
mne/time_frequency/ar.py,sha256=Kn39Mk1rSjWT7AgDo10KyuUq9hbYQp-wjAlfFW7mFPA,2335
mne/time_frequency/csd.py,sha256=Huqgw8yGoxcC6VQS-6uGu_Q2MwYFG2S7-bevJqN5GVU,50272
mne/time_frequency/multitaper.py,sha256=eBD9z7g35ldt8qThhb8dygB3RlUWKbCoz_tUrHRKU3I,17488
mne/time_frequency/psd.py,sha256=idldDUPwSqbia4S-rIDcNWp0zHmqpSGok_FdRm_ygl0,7629
mne/time_frequency/spectrum.py,sha256=L6aTwsshH09o2koK-0sNrPsr4eoENsKN1yl5Hm4d9w8,46608
mne/time_frequency/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/time_frequency/tests/__pycache__/__init__.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_ar.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_csd.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_multitaper.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_psd.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_spectrum.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_stft.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_stockwell.cpython-311.pyc,,
mne/time_frequency/tests/__pycache__/test_tfr.cpython-311.pyc,,
mne/time_frequency/tests/test_ar.py,sha256=I-3K1HxwU5LD_xUGBKY2igbNVRMi3ABIeyP1t8E8N7k,1727
mne/time_frequency/tests/test_csd.py,sha256=xhPs_h8Zk1skvalNrWSiNuXYWF4kshBHww2AAkcS4So,21268
mne/time_frequency/tests/test_multitaper.py,sha256=86SwWAsyXF4d1qjoYQOIFAsD2HMYROYMNCNdCqTx_-Q,2922
mne/time_frequency/tests/test_psd.py,sha256=VdJFCTW9RWmlVuexE9Iz-Ud3wAdx4kSkFFS_UaYIT3U,8129
mne/time_frequency/tests/test_spectrum.py,sha256=OV16f8gGSFsQa7uHNgAAPlsLx2fvKumeKJzvUclTkO4,13030
mne/time_frequency/tests/test_stft.py,sha256=irFC-2DfoDbmRQ6GKiJWxUX_KqOP-3bcvOluJ0JWDO8,2204
mne/time_frequency/tests/test_stockwell.py,sha256=KP8fMnhpyK43obm197MiiTe4HoQnfCav-J6-B3ex6b8,5365
mne/time_frequency/tests/test_tfr.py,sha256=FnH1LSvjsRJVjLt54-rhCQVTcFPgmECZ_uFr3FckKDM,50874
mne/time_frequency/tfr.py,sha256=EVOh4eIAIEOyw1BPJtxsLYLQ4nI04KqSVw9-wSu3YlA,111141
mne/transforms.py,sha256=RbMrVAr2Km5VeWMYvovK35UddEdlmFleWr_3Z9JmrYY,66966
mne/utils/__init__.py,sha256=FNYJ4nwkzCGe7arl7Omux6449tQjKbQ3FtfeAyMrN3A,4494
mne/utils/__pycache__/__init__.cpython-311.pyc,,
mne/utils/__pycache__/_bunch.cpython-311.pyc,,
mne/utils/__pycache__/_logging.cpython-311.pyc,,
mne/utils/__pycache__/_testing.cpython-311.pyc,,
mne/utils/__pycache__/check.cpython-311.pyc,,
mne/utils/__pycache__/config.cpython-311.pyc,,
mne/utils/__pycache__/dataframe.cpython-311.pyc,,
mne/utils/__pycache__/docs.cpython-311.pyc,,
mne/utils/__pycache__/fetching.cpython-311.pyc,,
mne/utils/__pycache__/linalg.cpython-311.pyc,,
mne/utils/__pycache__/misc.cpython-311.pyc,,
mne/utils/__pycache__/mixin.cpython-311.pyc,,
mne/utils/__pycache__/numerics.cpython-311.pyc,,
mne/utils/__pycache__/progressbar.cpython-311.pyc,,
mne/utils/__pycache__/spectrum.cpython-311.pyc,,
mne/utils/_bunch.py,sha256=niF0U2KkSuyGz-ETJhQknU6hlzVsGiIQ90_IG3Ge1u8,3035
mne/utils/_logging.py,sha256=eui3AsX4b0VPXY2tzwYPXbEmnjNvqQ2bKg_6H7KFQO0,17017
mne/utils/_testing.py,sha256=EsJAnHz67vbNfvOxk71v50gIfuEK3bmIMJT5yG_HZDI,14114
mne/utils/check.py,sha256=GKRjF7cfXbiafepO-onlotP0kW2ChzlDt1AIHicQO94,40830
mne/utils/config.py,sha256=iFe2p4R4TXpCFprttI2R8av8zvpVERLAQ_k49qv4uGg,26858
mne/utils/dataframe.py,sha256=d54l1-a3Du3V3XBDNXdOuwE0A_AXmNMK8y_wgCuc1wQ,3772
mne/utils/docs.py,sha256=1LQDP6kGatgtRLO4YHAxRtY-Rw6DpyFyvPhXQh7kKBE,174857
mne/utils/fetching.py,sha256=YiwCwa49jLOcILvhKblBGEizWpWfmfrB3LFWHeqF0LI,583
mne/utils/linalg.py,sha256=dALKt2gLEHdq8dmKBSDRiRnDZunwO2njjTMMxbWnZ14,6450
mne/utils/misc.py,sha256=IFXd4oTqIr7DSuTOmeL6bR5AjTPhpR2IRbfWU8dx4oU,15198
mne/utils/mixin.py,sha256=1F0z2gfq2W_u74ijJEJ2q6KyeE06gnD3yT0H04Q5QaU,26893
mne/utils/numerics.py,sha256=tMVdLH6Z8bt5S-yBUNxtVzkBeledxXlilr67VYAstUE,38080
mne/utils/progressbar.py,sha256=Rr1Hbo7IOCOXHAhpVAsLhKZGvN9Tm43Vo2I_zTj5dzg,6724
mne/utils/spectrum.py,sha256=IiTYM5ti1s28FziDH6cCrHOkp9RDZd5Y3PcvyBp3n10,2930
mne/utils/tests/__pycache__/test_bunch.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_check.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_config.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_docs.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_linalg.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_logging.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_misc.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_mixin.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_numerics.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_progressbar.cpython-311.pyc,,
mne/utils/tests/__pycache__/test_testing.cpython-311.pyc,,
mne/utils/tests/test_bunch.py,sha256=bQtNvHiqXspVFEz5L5WQwvlBqFJ5sz3q6yXvhoW5Zaw,643
mne/utils/tests/test_check.py,sha256=8VYHxNovI1_iYcGLICHIjVCvIRIU5qnL32Lx-y2bHHo,11942
mne/utils/tests/test_config.py,sha256=kN-uGXhmvUrgJ4z9y1AbWqgs__bcKuN5T8UD2MV_qQc,4667
mne/utils/tests/test_docs.py,sha256=0QRocl43SqLpJtO2KEaLH9zcWgkpnyPwlbGrz6HVbXg,5783
mne/utils/tests/test_linalg.py,sha256=tjooIdo4EMGHZlWXtl1w5U_0zXWNxC6Yyir9AWl24g8,3668
mne/utils/tests/test_logging.py,sha256=PQWdGvgU6ciX5cssJSbNg3mIQRQCxLVQIX15WxxTiVk,8439
mne/utils/tests/test_misc.py,sha256=gc0EFsIuYiX5eAfHx6OBuGDA94LX_Q3u8FU95HL3Xcc,4102
mne/utils/tests/test_mixin.py,sha256=jecBd9jPB4_RkYkUOXQWGVJbCL_SG-qiHabNWAn6sq4,1646
mne/utils/tests/test_numerics.py,sha256=0G4u5lv43e_136rJ66a3iI2Sn3jIi6BdM8c53ba__5M,21285
mne/utils/tests/test_progressbar.py,sha256=NywUdi8RFN17Xyc0Dv9uYhsovjdgoM49KCm0lzwhQyI,4324
mne/utils/tests/test_testing.py,sha256=H8acRW09NZa1uemmxhuJN3q_MiNIkmmJZkdNi2UX1WI,1569
mne/viz/_3d.py,sha256=K8-6Qe2oaADmPWFWToui_5KDt04tkC1fYiH0TTyLUfk,144063
mne/viz/_3d_overlay.py,sha256=jLsyw8DUOEAzssh48Hb5TAPy6ZCyImQEoho1p_e31I8,5679
mne/viz/__init__.py,sha256=4ZmvopCTKscjKbHvrW4XRJe9Bk8ryiFD47UDC45DmGQ,2141
mne/viz/__pycache__/_3d.cpython-311.pyc,,
mne/viz/__pycache__/_3d_overlay.cpython-311.pyc,,
mne/viz/__pycache__/__init__.cpython-311.pyc,,
mne/viz/__pycache__/_dipole.cpython-311.pyc,,
mne/viz/__pycache__/_figure.cpython-311.pyc,,
mne/viz/__pycache__/_mpl_figure.cpython-311.pyc,,
mne/viz/__pycache__/_proj.cpython-311.pyc,,
mne/viz/__pycache__/_scraper.cpython-311.pyc,,
mne/viz/__pycache__/circle.cpython-311.pyc,,
mne/viz/__pycache__/conftest.cpython-311.pyc,,
mne/viz/__pycache__/epochs.cpython-311.pyc,,
mne/viz/__pycache__/evoked.cpython-311.pyc,,
mne/viz/__pycache__/ica.cpython-311.pyc,,
mne/viz/__pycache__/misc.cpython-311.pyc,,
mne/viz/__pycache__/montage.cpython-311.pyc,,
mne/viz/__pycache__/raw.cpython-311.pyc,,
mne/viz/__pycache__/topo.cpython-311.pyc,,
mne/viz/__pycache__/topomap.cpython-311.pyc,,
mne/viz/__pycache__/utils.cpython-311.pyc,,
mne/viz/_brain/__init__.py,sha256=Pr2yTqgm6szX21XIV9jMXTXahVMq3GVTotQK8NQcLPU,510
mne/viz/_brain/__pycache__/__init__.cpython-311.pyc,,
mne/viz/_brain/__pycache__/_brain.cpython-311.pyc,,
mne/viz/_brain/__pycache__/_linkviewer.cpython-311.pyc,,
mne/viz/_brain/__pycache__/_scraper.cpython-311.pyc,,
mne/viz/_brain/__pycache__/callback.cpython-311.pyc,,
mne/viz/_brain/__pycache__/colormap.cpython-311.pyc,,
mne/viz/_brain/__pycache__/surface.cpython-311.pyc,,
mne/viz/_brain/__pycache__/view.cpython-311.pyc,,
mne/viz/_brain/_brain.py,sha256=DeAUTiZYv4F0AH7vsTCRy2zGy4wEzmD2kK3Y_wBIJgY,157365
mne/viz/_brain/_linkviewer.py,sha256=fj2jmeT2KbmOZxj6Nr3z6v5s35hU7HhDddwBv9K23fM,5432
mne/viz/_brain/_scraper.py,sha256=BHutqc1aLYdyDRAVPhKkkQEeh2XKzgFIMUjG5AK0VWY,4034
mne/viz/_brain/callback.py,sha256=FxeLmcvRvMwR4-WmZyUIKleKxgcH_iUMErJZJxFHLao,3714
mne/viz/_brain/colormap.py,sha256=fxRsQgAhkipNg42FfAhgEa3OZQ1lgXbofANTrvCyMik,6463
mne/viz/_brain/surface.py,sha256=kwsS_ux5hOzKLrLabPBl007eWow0P7vtG_6R5A3YxU4,6497
mne/viz/_brain/tests/__pycache__/test_brain.cpython-311.pyc,,
mne/viz/_brain/tests/__pycache__/test_notebook.cpython-311.pyc,,
mne/viz/_brain/tests/test_brain.py,sha256=rAN4t0JQSjjCSX0WfX7TEifEphUKotE07ikxmbVaeQs,46517
mne/viz/_brain/tests/test_notebook.py,sha256=6zaQQLQm8_V89eOHkEe_2N87zn4EGt1GuoSYhBN3Ork,5587
mne/viz/_brain/view.py,sha256=n3QREVVqGLIRIzXwxY9XEwIK4-5_0afhg87rHKlPMd4,2455
mne/viz/_dipole.py,sha256=0Eh6Jagqycrxkl3CuAIyBearseu0s87YMKVeLLcVHVA,7020
mne/viz/_figure.py,sha256=9m4ziZnoSVuBZwWr8QkfCqa9GTDC5jPxQiNvHF1x4xU,31926
mne/viz/_mpl_figure.py,sha256=J-JUQ27mrwd04OAXsdLB0A8mlOQ02fctdL5msPmzby4,106644
mne/viz/_proj.py,sha256=da3n733BbT2J9E6OK4EsWSUu3f6Zyx1jfil0go6WJFU,9716
mne/viz/_scraper.py,sha256=nSGZ9Hd_QfBV9nQiW52LRr9y2W7_m_Yn5fGEkjzCVAc,2817
mne/viz/backends/__init__.py,sha256=sCIj9bFi-CwSxYnU-GIgXqEB3Bbr4o0KiiYSxFvmsKo,53
mne/viz/backends/__pycache__/__init__.cpython-311.pyc,,
mne/viz/backends/__pycache__/_abstract.cpython-311.pyc,,
mne/viz/backends/__pycache__/_notebook.cpython-311.pyc,,
mne/viz/backends/__pycache__/_pyvista.cpython-311.pyc,,
mne/viz/backends/__pycache__/_qt.cpython-311.pyc,,
mne/viz/backends/__pycache__/_utils.cpython-311.pyc,,
mne/viz/backends/__pycache__/renderer.cpython-311.pyc,,
mne/viz/backends/_abstract.py,sha256=K1_ixaPG8vnct60oJ-3-ohXA6qwLJW_BWG9paNQr9X0,37818
mne/viz/backends/_notebook.py,sha256=mffO01rdiFDwOXB9ryzGDNSu5nOaWu0OgwL3fJB8Evs,51205
mne/viz/backends/_pyvista.py,sha256=1SEp9kVq1lydt5OQ_rAbsrIN0ioc9otEzxk2G36kMwA,47259
mne/viz/backends/_qt.py,sha256=tkp-7q-L61AkALgbhRCxMiyu0Y7MVgGe5aErCo_8wEw,60513
mne/viz/backends/_utils.py,sha256=MMH8z4vYS7IqWeOxP2WQmbl2IscWZeLWa8-bDISlIY8,15557
mne/viz/backends/renderer.py,sha256=V4IkieiVEOqNzcKagq_BaDCunsgJ1oCGrGznr-y38Bs,11950
mne/viz/backends/tests/__pycache__/_utils.cpython-311.pyc,,
mne/viz/backends/tests/__pycache__/test_abstract.cpython-311.pyc,,
mne/viz/backends/tests/__pycache__/test_renderer.cpython-311.pyc,,
mne/viz/backends/tests/__pycache__/test_utils.cpython-311.pyc,,
mne/viz/backends/tests/_utils.py,sha256=EzazKpK0kiYseS52AdR4kfE8Y1DsGQL80u5tfguGt7E,1164
mne/viz/backends/tests/test_abstract.py,sha256=s5YCf7yYa0tmYHK1UrM7YSTFl3hfcHl1fGmVUr-zzN4,4553
mne/viz/backends/tests/test_renderer.py,sha256=NjnLq-aGj2glrY2irPRFet4Vo0N_hmiq9IJIWq3PNVY,7437
mne/viz/backends/tests/test_utils.py,sha256=ytcTK2upL0gxxC-opZfK0x6B5r6p7JtRfTgnVzTLy1s,3869
mne/viz/circle.py,sha256=hz2678nAWHeHGbfjqVtuYQMGfTE5gaMO0Y49nrK-0a4,15169
mne/viz/conftest.py,sha256=sDOYcghtzxHTKyhxUhZe0bSZCvbEndM_xQgEkjQJcBY,1633
mne/viz/epochs.py,sha256=zjlE0x5ywiC6Soh6HgK2-JUcuCxo49Qv4NGbrHIZuhg,44011
mne/viz/evoked.py,sha256=xmToK5F2EcoSh2zXv2gvwYItySh8fbrwiIDdn19HMMY,117610
mne/viz/ica.py,sha256=KHZoUvq-NBOxko_nlBEhW0Fpu-UFSwTORkLUGH_YYqQ,46013
mne/viz/misc.py,sha256=IeH1kwhWoJYRZC2E-x8yMqFSqQUE_1YOJcFQcEDC8nw,54351
mne/viz/montage.py,sha256=--5phjue6Tz5CCk6Lx2XRSNMTxwaraobVtZhTpxBmXc,2847
mne/viz/raw.py,sha256=xxoLzX9TUej80_B_AuJ6QTrYkaeafBFcV5VwxInlLx4,20840
mne/viz/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mne/viz/tests/__pycache__/__init__.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_3d.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_3d_mpl.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_circle.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_epochs.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_evoked.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_figure.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_ica.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_misc.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_montage.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_proj.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_raw.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_scraper.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_topo.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_topomap.cpython-311.pyc,,
mne/viz/tests/__pycache__/test_utils.cpython-311.pyc,,
mne/viz/tests/test_3d.py,sha256=zZYVYT2v1oguv11VhORYkPyHATWdo6JHfMTsaMVunaA,38878
mne/viz/tests/test_3d_mpl.py,sha256=aQx-IQmBjtdNjETtrW6hrX9-EkpQhF-0v32AMcXOuvc,5336
mne/viz/tests/test_circle.py,sha256=cUIYrJLpLRD_xd_2BkV-zhEwpfvxfU5ksHV98O-Qc2s,1054
mne/viz/tests/test_epochs.py,sha256=JEXgFJ6Z1QMyIvAKRvAuhIJ2SjTkm0fa8lEV_84zeFs,20218
mne/viz/tests/test_evoked.py,sha256=4fGk9BXw8WhXgSkdZKmOKWsR8Daj06NmPjHImU5MJnw,23911
mne/viz/tests/test_figure.py,sha256=MtpwX4Ma_7zJCggWQrUINTYqi7YXORkNRsMWRKFkN6M,366
mne/viz/tests/test_ica.py,sha256=gsGiCOBWC0NI_T70YXKR271Hces-PC9DaSJvx_r7uxs,18351
mne/viz/tests/test_misc.py,sha256=ampAfqHtV1ycp6gU3iaJOSNLlvMJF7jwD3MaMuCiV_k,11878
mne/viz/tests/test_montage.py,sha256=cCodyVT0NIe0K4uzNwRkuJ-oIrJR6GCQcHkLiemfAQ8,2757
mne/viz/tests/test_proj.py,sha256=-vRgdIEcrdLhH6-p7PmAXDkY71zIy4pyqii56t6Xq8Q,2235
mne/viz/tests/test_raw.py,sha256=8bMuqIEgMoPORWOy8o1_4HwVK4mA2L3fMCkNTTbvwRs,47184
mne/viz/tests/test_scraper.py,sha256=kAcIfs5MDCPPx2et2H6FDcOjsMvw0WJhrk9VAWmXEFs,970
mne/viz/tests/test_topo.py,sha256=g4jsRPt3wdPQznrHD1tUrY02X_V6pBczhqbk_69Nb2w,13267
mne/viz/tests/test_topomap.py,sha256=xzcgx2AFlsZPcEfQf2wRGXJPYBN1xphjQrZ2V9MdKGE,33776
mne/viz/tests/test_utils.py,sha256=rwo9gU8tEREP20VYt-HJeBnuXn1Kv3didOHoiK9dtJs,6990
mne/viz/topo.py,sha256=a97dO9JiayckNVwFCWcb-GrfWgGHjVUBbKy7bJa9PhE,40163
mne/viz/topomap.py,sha256=MDpN5W7eNW5ZGJ0K61Q_dgQjrMtTsjI0nJ6-LumZmIY,129045
mne/viz/utils.py,sha256=aRmncXuSrg1iGSlJNfZGJ7Jc-SFYKsDSNLzyKKGyAY0,100152
