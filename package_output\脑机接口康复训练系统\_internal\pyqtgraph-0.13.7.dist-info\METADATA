Metadata-Version: 2.1
Name: pyqtgraph
Version: 0.13.7
Summary: Scientific Graphics and GUI Library for Python
Home-page: http://www.pyqtgraph.org
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Documentation, https://pyqtgraph.readthedocs.io
Project-URL: Source, https://github.com/pyqtgraph/pyqtgraph
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.9
License-File: LICENSE.txt
Requires-Dist: numpy >=1.22.0

PyQtGraph is a pure-python graphics and GUI library built on PyQt5/PySide2 and
numpy. 

It is intended for use in mathematics / scientific / engineering applications.
Despite being written entirely in python, the library is very fast due to its
heavy leverage of numpy for number crunching, Qt's GraphicsView framework for
2D display, and OpenGL for 3D display.
