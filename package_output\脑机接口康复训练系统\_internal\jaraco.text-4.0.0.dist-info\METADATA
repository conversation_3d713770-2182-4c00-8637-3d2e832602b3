Metadata-Version: 2.1
Name: jaraco.text
Version: 4.0.0
Summary: Module for text manipulation
Author-email: "<PERSON>" <<EMAIL>>
Project-URL: Source, https://github.com/jaraco/jaraco.text
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: jaraco.functools
Requires-Dist: jaraco.context >=4.1
Requires-Dist: autocommand
Requires-Dist: more-itertools
Requires-Dist: importlib-resources ; python_version < "3.9"
Provides-Extra: doc
Requires-Dist: sphinx >=3.5 ; extra == 'doc'
Requires-Dist: jaraco.packaging >=9.3 ; extra == 'doc'
Requires-Dist: rst.linker >=1.9 ; extra == 'doc'
Requires-Dist: furo ; extra == 'doc'
Requires-Dist: sphinx-lint ; extra == 'doc'
Requires-Dist: jaraco.tidelift >=1.4 ; extra == 'doc'
Provides-Extra: inflect
Requires-Dist: inflect ; extra == 'inflect'
Provides-Extra: test
Requires-Dist: pytest !=8.1.*,>=6 ; extra == 'test'
Requires-Dist: pytest-checkdocs >=2.4 ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: pytest-mypy ; extra == 'test'
Requires-Dist: pytest-enabler >=2.2 ; extra == 'test'
Requires-Dist: pathlib2 ; (python_version < "3.10") and extra == 'test'
Requires-Dist: pytest-ruff >=0.2.1 ; (sys_platform != "cygwin") and extra == 'test'

.. image:: https://img.shields.io/pypi/v/jaraco.text.svg
   :target: https://pypi.org/project/jaraco.text

.. image:: https://img.shields.io/pypi/pyversions/jaraco.text.svg

.. image:: https://github.com/jaraco/jaraco.text/actions/workflows/main.yml/badge.svg
   :target: https://github.com/jaraco/jaraco.text/actions?query=workflow%3A%22tests%22
   :alt: tests

.. image:: https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json
    :target: https://github.com/astral-sh/ruff
    :alt: Ruff

.. image:: https://readthedocs.org/projects/jaracotext/badge/?version=latest
   :target: https://jaracotext.readthedocs.io/en/latest/?badge=latest

.. image:: https://img.shields.io/badge/skeleton-2024-informational
   :target: https://blog.jaraco.com/skeleton

.. image:: https://tidelift.com/badges/package/pypi/jaraco.text
   :target: https://tidelift.com/subscription/pkg/pypi-jaraco.text?utm_source=pypi-jaraco.text&utm_medium=readme


This package provides handy routines for dealing with text, such as
wrapping, substitution, trimming, stripping, prefix and suffix removal,
line continuation, indentation, comment processing, identifier processing,
values parsing, case insensitive comparison, and more. See the docs
(linked in the badge above) for the detailed documentation and examples.

Layouts
=======

One of the features of this package is the layouts module, which
provides a simple example of translating keystrokes from one keyboard
layout to another::

    echo qwerty | python -m jaraco.text.to-dvorak
    ',.pyf
    echo  "',.pyf" | python -m jaraco.text.to-qwerty
    qwerty

Newline Reporting
=================

Need to know what newlines appear in a file?

::

    $ python -m jaraco.text.show-newlines README.rst
    newline is '\n'

For Enterprise
==============

Available as part of the Tidelift Subscription.

This project and the maintainers of thousands of other packages are working with Tidelift to deliver one enterprise subscription that covers all of the open source you use.

`Learn more <https://tidelift.com/subscription/pkg/pypi-jaraco.text?utm_source=pypi-jaraco.text&utm_medium=referral&utm_campaign=github>`_.
