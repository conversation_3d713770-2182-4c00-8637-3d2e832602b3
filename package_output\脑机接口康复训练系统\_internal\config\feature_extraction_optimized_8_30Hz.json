{"sampling_rate": 125, "n_channels": 8, "n_samples": 250, "fbcsp": {"enabled": true, "freq_bands": [[8, 12], [13, 20], [21, 30]], "n_components": 4, "reg": "<PERSON><PERSON>_wolf", "log": true, "_comment": "优化：扩展到3个频段覆盖完整8-30Hz，增加组件数"}, "riemannian": {"enabled": true, "metric": "<PERSON><PERSON><PERSON>", "estimator": "oas", "tangent_space": true, "reference_method": "<PERSON><PERSON><PERSON>", "regularization": 0.05, "_comment": "优化：改用oas估计器，降低正则化以适应8-30Hz频段"}, "tef": {"enabled": true, "time_features": {"mean": true, "std": true, "var": false, "skew": true, "kurtosis": false, "rms": true, "peak": false, "energy": true, "peak_to_peak": true}, "entropy_features": {"sample_entropy": false, "approximate_entropy": false, "permutation_entropy": false, "fuzzy_entropy": false, "multiscale_entropy": false}, "freq_features": {"power_spectral_density": true, "spectral_centroid": true, "spectral_bandwidth": true, "spectral_rolloff": false, "spectral_flatness": false, "spectral_crest": false, "dominant_frequency": true, "frequency_variance": true}, "freq_bands": [[8, 12], [13, 20], [21, 30]], "nperseg": 125, "_comment": "优化：扩展到3个频段，增加spectral_bandwidth和frequency_variance特征"}, "tangent_space": {"enabled": true, "estimator": "oas", "metric": "<PERSON><PERSON><PERSON>", "regularization": 0.001, "_comment": "优化：改用oas估计器，大幅降低正则化以适应新频段"}, "plv": {"enabled": true, "freq_bands": [[8, 12], [13, 20], [21, 30]], "channel_pairs": [[3, 5], [4, 3], [4, 5], [3, 1], [5, 2], [4, 0]], "filter_order": 6, "method": "hilbert", "window_length": 250, "overlap": 0.0, "normalize": true, "_comment_optimization": "优化：扩展到3个频段，增加通道对数量", "_comment_freq_bands": "μ节律(8-12Hz)，低β节律(13-20Hz)，高β节律(21-30Hz)", "_comment_channel_pairs": "基于8通道布局[PZ,P3,P4,C3,CZ,C4,F3,F4]的6个运动相关通道对", "_comment_pairs_detail": "C3-C4, CZ-C3, CZ-C4, C3-P3, C4-P4, CZ-PZ", "_comment_features": "3频段×6通道对=18个PLV特征，比原来的4个特征大幅增加"}, "fusion": {"method": "concatenate", "normalize": true, "feature_selection": false, "selection_method": "mutual_info", "n_features": null, "pca_components": null, "pca_variance_ratio": 0.95}, "enable_caching": true, "cache_dir": "data/cache/features", "max_cache_size": 1000, "log_level": "INFO", "log_performance": true}