('D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\PYZ-00.pyz',
 [('PIL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-1'),
  ('PIL.AvifImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BlpImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BmpImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BufrStubImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.CurImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DcxImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DdsImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.EpsImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ExifTags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-1'),
  ('PIL.FitsImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FliImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FpxImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FtexImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GbrImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GifImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GimpGradientFile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-1'),
  ('PIL.GimpPaletteFile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-1'),
  ('PIL.GribStubImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Hdf5StubImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcnsImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcoImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ImImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Image',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-1'),
  ('PIL.ImageChops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-1'),
  ('PIL.ImageCms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-1'),
  ('PIL.ImageColor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-1'),
  ('PIL.ImageFile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-1'),
  ('PIL.ImageFilter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-1'),
  ('PIL.ImageMath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-1'),
  ('PIL.ImageMode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-1'),
  ('PIL.ImageOps',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-1'),
  ('PIL.ImagePalette',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-1'),
  ('PIL.ImageQt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-1'),
  ('PIL.ImageSequence',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-1'),
  ('PIL.ImageShow',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-1'),
  ('PIL.ImageTk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-1'),
  ('PIL.ImageWin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-1'),
  ('PIL.ImtImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IptcImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Jpeg2KImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegPresets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-1'),
  ('PIL.McIdasImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MicImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpegImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpoImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MspImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PaletteFile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-1'),
  ('PIL.PalmImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcdImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcxImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfParser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-1'),
  ('PIL.PixarImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PngImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PpmImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PsdImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.QoiImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SgiImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SpiderImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SunImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TgaImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffTags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-1'),
  ('PIL.WebPImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.WmfImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XVThumbImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XbmImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XpmImagePlugin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL._binary',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-1'),
  ('PIL._deprecate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-1'),
  ('PIL._typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-1'),
  ('PIL._util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-1'),
  ('PIL._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-1'),
  ('PIL.features',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-1'),
  ('PySide6',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE-1'),
  ('PySide6.support',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE-1'),
  ('PySide6.support.deprecated',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE-1'),
  ('__future__',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\__future__.py',
   'PYMODULE-1'),
  ('_aix_support',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_aix_support.py',
   'PYMODULE-1'),
  ('_bootsubprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_bootsubprocess.py',
   'PYMODULE-1'),
  ('_compat_pickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_compat_pickle.py',
   'PYMODULE-1'),
  ('_compression',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_compression.py',
   'PYMODULE-1'),
  ('_distutils_hack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE-1'),
  ('_distutils_hack.override',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE-1'),
  ('_osx_support',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_osx_support.py',
   'PYMODULE-1'),
  ('_py_abc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_py_abc.py',
   'PYMODULE-1'),
  ('_pydecimal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_pydecimal.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils._win32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils.qt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-1'),
  ('_pyi_rth_utils.tempfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE-1'),
  ('_sitebuiltins',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_sitebuiltins.py',
   'PYMODULE-1'),
  ('_strptime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_strptime.py',
   'PYMODULE-1'),
  ('_threading_local',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\_threading_local.py',
   'PYMODULE-1'),
  ('app', 'D:\\NK_Python\\脑机接口康复训练\\app\\__init__.py', 'PYMODULE-1'),
  ('app.application',
   'D:\\NK_Python\\脑机接口康复训练\\app\\application.py',
   'PYMODULE-1'),
  ('app.background_loader',
   'D:\\NK_Python\\脑机接口康复训练\\app\\background_loader.py',
   'PYMODULE-1'),
  ('app.config', 'D:\\NK_Python\\脑机接口康复训练\\app\\config.py', 'PYMODULE-1'),
  ('argparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\argparse.py',
   'PYMODULE-1'),
  ('ast',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ast.py',
   'PYMODULE-1'),
  ('asyncio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\__init__.py',
   'PYMODULE-1'),
  ('asyncio.base_events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\base_events.py',
   'PYMODULE-1'),
  ('asyncio.base_futures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-1'),
  ('asyncio.base_subprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-1'),
  ('asyncio.base_tasks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-1'),
  ('asyncio.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\constants.py',
   'PYMODULE-1'),
  ('asyncio.coroutines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-1'),
  ('asyncio.events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\events.py',
   'PYMODULE-1'),
  ('asyncio.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-1'),
  ('asyncio.format_helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-1'),
  ('asyncio.futures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\futures.py',
   'PYMODULE-1'),
  ('asyncio.locks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\locks.py',
   'PYMODULE-1'),
  ('asyncio.log',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\log.py',
   'PYMODULE-1'),
  ('asyncio.mixins',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\mixins.py',
   'PYMODULE-1'),
  ('asyncio.proactor_events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-1'),
  ('asyncio.protocols',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\protocols.py',
   'PYMODULE-1'),
  ('asyncio.queues',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\queues.py',
   'PYMODULE-1'),
  ('asyncio.runners',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\runners.py',
   'PYMODULE-1'),
  ('asyncio.selector_events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-1'),
  ('asyncio.sslproto',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-1'),
  ('asyncio.staggered',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\staggered.py',
   'PYMODULE-1'),
  ('asyncio.streams',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\streams.py',
   'PYMODULE-1'),
  ('asyncio.subprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-1'),
  ('asyncio.taskgroups',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-1'),
  ('asyncio.tasks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\tasks.py',
   'PYMODULE-1'),
  ('asyncio.threads',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\threads.py',
   'PYMODULE-1'),
  ('asyncio.timeouts',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-1'),
  ('asyncio.transports',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\transports.py',
   'PYMODULE-1'),
  ('asyncio.trsock',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\trsock.py',
   'PYMODULE-1'),
  ('asyncio.unix_events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-1'),
  ('backports',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\backports\\__init__.py',
   'PYMODULE-1'),
  ('backports.tarfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\backports\\tarfile\\__init__.py',
   'PYMODULE-1'),
  ('backports.tarfile.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE-1'),
  ('backports.tarfile.compat.py38',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE-1'),
  ('base64',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\base64.py',
   'PYMODULE-1'),
  ('bisect',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\bisect.py',
   'PYMODULE-1'),
  ('bleak',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\__init__.py',
   'PYMODULE-1'),
  ('bleak.args',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\args\\__init__.py',
   'PYMODULE-1'),
  ('bleak.args.bluez',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\args\\bluez.py',
   'PYMODULE-1'),
  ('bleak.args.corebluetooth',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\args\\corebluetooth.py',
   'PYMODULE-1'),
  ('bleak.args.winrt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\args\\winrt.py',
   'PYMODULE-1'),
  ('bleak.assigned_numbers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\assigned_numbers.py',
   'PYMODULE-1'),
  ('bleak.backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\__init__.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\__init__.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.advertisement_monitor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\advertisement_monitor.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\client.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.defs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\defs.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.manager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\manager.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\scanner.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.signals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\signals.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\utils.py',
   'PYMODULE-1'),
  ('bleak.backends.bluezdbus.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\bluezdbus\\version.py',
   'PYMODULE-1'),
  ('bleak.backends.characteristic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\characteristic.py',
   'PYMODULE-1'),
  ('bleak.backends.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\client.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\__init__.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth.CentralManagerDelegate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\CentralManagerDelegate.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth.PeripheralDelegate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\PeripheralDelegate.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\client.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\scanner.py',
   'PYMODULE-1'),
  ('bleak.backends.corebluetooth.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\corebluetooth\\utils.py',
   'PYMODULE-1'),
  ('bleak.backends.descriptor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\descriptor.py',
   'PYMODULE-1'),
  ('bleak.backends.device',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\device.py',
   'PYMODULE-1'),
  ('bleak.backends.p4android',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\p4android\\__init__.py',
   'PYMODULE-1'),
  ('bleak.backends.p4android.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\p4android\\client.py',
   'PYMODULE-1'),
  ('bleak.backends.p4android.defs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\p4android\\defs.py',
   'PYMODULE-1'),
  ('bleak.backends.p4android.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\p4android\\scanner.py',
   'PYMODULE-1'),
  ('bleak.backends.p4android.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\p4android\\utils.py',
   'PYMODULE-1'),
  ('bleak.backends.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\scanner.py',
   'PYMODULE-1'),
  ('bleak.backends.service',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\service.py',
   'PYMODULE-1'),
  ('bleak.backends.winrt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\winrt\\__init__.py',
   'PYMODULE-1'),
  ('bleak.backends.winrt.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\winrt\\client.py',
   'PYMODULE-1'),
  ('bleak.backends.winrt.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\winrt\\scanner.py',
   'PYMODULE-1'),
  ('bleak.backends.winrt.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\backends\\winrt\\util.py',
   'PYMODULE-1'),
  ('bleak.exc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\exc.py',
   'PYMODULE-1'),
  ('bleak.uuids',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\bleak\\uuids.py',
   'PYMODULE-1'),
  ('bz2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\bz2.py',
   'PYMODULE-1'),
  ('cProfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\cProfile.py',
   'PYMODULE-1'),
  ('calendar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\calendar.py',
   'PYMODULE-1'),
  ('certifi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-1'),
  ('certifi.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-1'),
  ('cffi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE-1'),
  ('cffi._imp_emulation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE-1'),
  ('cffi._shimmed_dist_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE-1'),
  ('cffi.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE-1'),
  ('cffi.cffi_opcode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE-1'),
  ('cffi.commontypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE-1'),
  ('cffi.cparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE-1'),
  ('cffi.error',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE-1'),
  ('cffi.ffiplatform',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE-1'),
  ('cffi.lock',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE-1'),
  ('cffi.model',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE-1'),
  ('cffi.pkgconfig',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE-1'),
  ('cffi.recompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE-1'),
  ('cffi.vengine_cpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE-1'),
  ('cffi.vengine_gen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE-1'),
  ('cffi.verifier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE-1'),
  ('cgi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\cgi.py',
   'PYMODULE-1'),
  ('charset_normalizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-1'),
  ('charset_normalizer.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-1'),
  ('charset_normalizer.cd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-1'),
  ('charset_normalizer.constant',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-1'),
  ('charset_normalizer.legacy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-1'),
  ('charset_normalizer.models',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-1'),
  ('charset_normalizer.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-1'),
  ('charset_normalizer.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-1'),
  ('cmd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\cmd.py',
   'PYMODULE-1'),
  ('code',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\code.py',
   'PYMODULE-1'),
  ('codeop',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\codeop.py',
   'PYMODULE-1'),
  ('colorama',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE-1'),
  ('colorama.ansi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE-1'),
  ('colorama.ansitowin32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE-1'),
  ('colorama.initialise',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE-1'),
  ('colorama.win32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE-1'),
  ('colorama.winterm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE-1'),
  ('colorsys',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\colorsys.py',
   'PYMODULE-1'),
  ('comtypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.GUID',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\GUID.py',
   'PYMODULE-1'),
  ('comtypes._comobject',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_comobject.py',
   'PYMODULE-1'),
  ('comtypes._memberspec',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_memberspec.py',
   'PYMODULE-1'),
  ('comtypes._meta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_meta.py',
   'PYMODULE-1'),
  ('comtypes._npsupport',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_npsupport.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\__init__.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit._cominterface_meta_patcher',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\_cominterface_meta_patcher.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit.bstr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\bstr.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit.instancemethod',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\instancemethod.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\misc.py',
   'PYMODULE-1'),
  ('comtypes._post_coinit.unknwn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_post_coinit\\unknwn.py',
   'PYMODULE-1'),
  ('comtypes._safearray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_safearray.py',
   'PYMODULE-1'),
  ('comtypes._tlib_version_checker',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_tlib_version_checker.py',
   'PYMODULE-1'),
  ('comtypes._vtbl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\_vtbl.py',
   'PYMODULE-1'),
  ('comtypes.automation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\automation.py',
   'PYMODULE-1'),
  ('comtypes.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.client._activeobj',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_activeobj.py',
   'PYMODULE-1'),
  ('comtypes.client._code_cache',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_code_cache.py',
   'PYMODULE-1'),
  ('comtypes.client._constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_constants.py',
   'PYMODULE-1'),
  ('comtypes.client._create',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_create.py',
   'PYMODULE-1'),
  ('comtypes.client._events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_events.py',
   'PYMODULE-1'),
  ('comtypes.client._generate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_generate.py',
   'PYMODULE-1'),
  ('comtypes.client._managing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\_managing.py',
   'PYMODULE-1'),
  ('comtypes.client.dynamic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\dynamic.py',
   'PYMODULE-1'),
  ('comtypes.client.lazybind',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\client\\lazybind.py',
   'PYMODULE-1'),
  ('comtypes.connectionpoints',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\connectionpoints.py',
   'PYMODULE-1'),
  ('comtypes.errorinfo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\errorinfo.py',
   'PYMODULE-1'),
  ('comtypes.gen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\gen\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.gen.SpeechLib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\gen\\SpeechLib.py',
   'PYMODULE-1'),
  ('comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\gen\\_00020430_0000_0000_C000_000000000046_0_2_0.py',
   'PYMODULE-1'),
  ('comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\gen\\_C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4.py',
   'PYMODULE-1'),
  ('comtypes.hresult',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\hresult.py',
   'PYMODULE-1'),
  ('comtypes.messageloop',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\messageloop.py',
   'PYMODULE-1'),
  ('comtypes.patcher',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\patcher.py',
   'PYMODULE-1'),
  ('comtypes.persist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\persist.py',
   'PYMODULE-1'),
  ('comtypes.safearray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\safearray.py',
   'PYMODULE-1'),
  ('comtypes.server',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\server\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.stream',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\stream.py',
   'PYMODULE-1'),
  ('comtypes.tools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\__init__.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.codegenerator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\codegenerator.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.comments',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\comments.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.heads',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\heads.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\helpers.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.modulenamer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\modulenamer.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.namespaces',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\namespaces.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.packing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\packing.py',
   'PYMODULE-1'),
  ('comtypes.tools.codegenerator.typeannotator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\typeannotator.py',
   'PYMODULE-1'),
  ('comtypes.tools.tlbparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\tlbparser.py',
   'PYMODULE-1'),
  ('comtypes.tools.typedesc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\typedesc.py',
   'PYMODULE-1'),
  ('comtypes.tools.typedesc_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\tools\\typedesc_base.py',
   'PYMODULE-1'),
  ('comtypes.typeinfo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\comtypes\\typeinfo.py',
   'PYMODULE-1'),
  ('concurrent',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\concurrent\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-1'),
  ('concurrent.futures.process',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-1'),
  ('concurrent.futures.thread',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-1'),
  ('configparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\configparser.py',
   'PYMODULE-1'),
  ('contextlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\contextlib.py',
   'PYMODULE-1'),
  ('contextvars',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\contextvars.py',
   'PYMODULE-1'),
  ('contourpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE-1'),
  ('contourpy._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE-1'),
  ('contourpy.array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE-1'),
  ('contourpy.chunk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE-1'),
  ('contourpy.convert',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE-1'),
  ('contourpy.dechunk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE-1'),
  ('contourpy.enum_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE-1'),
  ('contourpy.typecheck',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE-1'),
  ('contourpy.types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE-1'),
  ('copy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\copy.py',
   'PYMODULE-1'),
  ('core', 'D:\\NK_Python\\脑机接口康复训练\\core\\__init__.py', 'PYMODULE-1'),
  ('core.database', 'D:\\NK_Python\\脑机接口康复训练\\core\\database.py', 'PYMODULE-1'),
  ('core.network_config',
   'D:\\NK_Python\\脑机接口康复训练\\core\\network_config.py',
   'PYMODULE-1'),
  ('core.simple_voice',
   'D:\\NK_Python\\脑机接口康复训练\\core\\simple_voice.py',
   'PYMODULE-1'),
  ('core.udp_communicator',
   'D:\\NK_Python\\脑机接口康复训练\\core\\udp_communicator.py',
   'PYMODULE-1'),
  ('cryptography',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.__about__',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE-1'),
  ('cryptography.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE-1'),
  ('cryptography.fernet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE-1'),
  ('cryptography.hazmat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat._oid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends.openssl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends.openssl.backend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit.ciphers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._asymmetric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._serialization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.constant_time',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.hashes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.hmac',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.padding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE-1'),
  ('cryptography.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE-1'),
  ('cryptography.x509',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.x509.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE-1'),
  ('cryptography.x509.certificate_transparency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE-1'),
  ('cryptography.x509.extensions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE-1'),
  ('cryptography.x509.general_name',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE-1'),
  ('cryptography.x509.name',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE-1'),
  ('cryptography.x509.oid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE-1'),
  ('cryptography.x509.verification',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE-1'),
  ('csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\csv.py',
   'PYMODULE-1'),
  ('ctypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\__init__.py',
   'PYMODULE-1'),
  ('ctypes._aix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\_aix.py',
   'PYMODULE-1'),
  ('ctypes._endian',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\_endian.py',
   'PYMODULE-1'),
  ('ctypes.macholib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-1'),
  ('ctypes.macholib.dyld',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-1'),
  ('ctypes.macholib.dylib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-1'),
  ('ctypes.macholib.framework',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-1'),
  ('ctypes.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\util.py',
   'PYMODULE-1'),
  ('ctypes.wintypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-1'),
  ('cycler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE-1'),
  ('dataclasses',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\dataclasses.py',
   'PYMODULE-1'),
  ('datetime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\datetime.py',
   'PYMODULE-1'),
  ('dateutil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-1'),
  ('dateutil._common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-1'),
  ('dateutil._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-1'),
  ('dateutil.easter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-1'),
  ('dateutil.parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.parser._parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-1'),
  ('dateutil.parser.isoparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-1'),
  ('dateutil.relativedelta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-1'),
  ('dateutil.rrule',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-1'),
  ('dateutil.tz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.tz._common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-1'),
  ('dateutil.tz._factories',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-1'),
  ('dateutil.tz.tz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-1'),
  ('dateutil.tz.win',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-1'),
  ('dateutil.zoneinfo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-1'),
  ('decimal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\decimal.py',
   'PYMODULE-1'),
  ('decorator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\decorator.py',
   'PYMODULE-1'),
  ('difflib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\difflib.py',
   'PYMODULE-1'),
  ('dis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\dis.py',
   'PYMODULE-1'),
  ('distutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\__init__.py',
   'PYMODULE-1'),
  ('distutils._msvccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE-1'),
  ('distutils.archive_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\archive_util.py',
   'PYMODULE-1'),
  ('distutils.ccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\ccompiler.py',
   'PYMODULE-1'),
  ('distutils.cmd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\cmd.py',
   'PYMODULE-1'),
  ('distutils.command',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE-1'),
  ('distutils.command.bdist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE-1'),
  ('distutils.command.build',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\command\\build.py',
   'PYMODULE-1'),
  ('distutils.command.build_ext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE-1'),
  ('distutils.command.sdist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE-1'),
  ('distutils.config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\config.py',
   'PYMODULE-1'),
  ('distutils.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\core.py',
   'PYMODULE-1'),
  ('distutils.debug',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\debug.py',
   'PYMODULE-1'),
  ('distutils.dep_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\dep_util.py',
   'PYMODULE-1'),
  ('distutils.dir_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\dir_util.py',
   'PYMODULE-1'),
  ('distutils.dist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\dist.py',
   'PYMODULE-1'),
  ('distutils.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\errors.py',
   'PYMODULE-1'),
  ('distutils.extension',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\extension.py',
   'PYMODULE-1'),
  ('distutils.fancy_getopt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE-1'),
  ('distutils.file_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\file_util.py',
   'PYMODULE-1'),
  ('distutils.filelist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\filelist.py',
   'PYMODULE-1'),
  ('distutils.log',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\log.py',
   'PYMODULE-1'),
  ('distutils.msvc9compiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE-1'),
  ('distutils.spawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\spawn.py',
   'PYMODULE-1'),
  ('distutils.sysconfig',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\sysconfig.py',
   'PYMODULE-1'),
  ('distutils.text_file',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\text_file.py',
   'PYMODULE-1'),
  ('distutils.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\util.py',
   'PYMODULE-1'),
  ('distutils.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\version.py',
   'PYMODULE-1'),
  ('distutils.versionpredicate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE-1'),
  ('email',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\__init__.py',
   'PYMODULE-1'),
  ('email._encoded_words',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\_encoded_words.py',
   'PYMODULE-1'),
  ('email._header_value_parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-1'),
  ('email._parseaddr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\_parseaddr.py',
   'PYMODULE-1'),
  ('email._policybase',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\_policybase.py',
   'PYMODULE-1'),
  ('email.base64mime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\base64mime.py',
   'PYMODULE-1'),
  ('email.charset',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\charset.py',
   'PYMODULE-1'),
  ('email.contentmanager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\contentmanager.py',
   'PYMODULE-1'),
  ('email.encoders',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\encoders.py',
   'PYMODULE-1'),
  ('email.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\errors.py',
   'PYMODULE-1'),
  ('email.feedparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\feedparser.py',
   'PYMODULE-1'),
  ('email.generator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\generator.py',
   'PYMODULE-1'),
  ('email.header',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\header.py',
   'PYMODULE-1'),
  ('email.headerregistry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\headerregistry.py',
   'PYMODULE-1'),
  ('email.iterators',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\iterators.py',
   'PYMODULE-1'),
  ('email.message',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\message.py',
   'PYMODULE-1'),
  ('email.parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\parser.py',
   'PYMODULE-1'),
  ('email.policy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\policy.py',
   'PYMODULE-1'),
  ('email.quoprimime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\quoprimime.py',
   'PYMODULE-1'),
  ('email.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\email\\utils.py',
   'PYMODULE-1'),
  ('fnmatch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\fnmatch.py',
   'PYMODULE-1'),
  ('fractions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\fractions.py',
   'PYMODULE-1'),
  ('ftplib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ftplib.py',
   'PYMODULE-1'),
  ('getopt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\getopt.py',
   'PYMODULE-1'),
  ('getpass',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\getpass.py',
   'PYMODULE-1'),
  ('gettext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\gettext.py',
   'PYMODULE-1'),
  ('glob',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\glob.py',
   'PYMODULE-1'),
  ('gzip',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\gzip.py',
   'PYMODULE-1'),
  ('hashlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\hashlib.py',
   'PYMODULE-1'),
  ('hmac',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\hmac.py',
   'PYMODULE-1'),
  ('html',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\html\\__init__.py',
   'PYMODULE-1'),
  ('html.entities',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\html\\entities.py',
   'PYMODULE-1'),
  ('http',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\http\\__init__.py',
   'PYMODULE-1'),
  ('http.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\http\\client.py',
   'PYMODULE-1'),
  ('http.cookiejar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\http\\cookiejar.py',
   'PYMODULE-1'),
  ('http.cookies',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\http\\cookies.py',
   'PYMODULE-1'),
  ('http.server',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\http\\server.py',
   'PYMODULE-1'),
  ('idna',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-1'),
  ('idna.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-1'),
  ('idna.idnadata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-1'),
  ('idna.intranges',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-1'),
  ('idna.package_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-1'),
  ('idna.uts46data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-1'),
  ('imp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\imp.py',
   'PYMODULE-1'),
  ('importlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\__init__.py',
   'PYMODULE-1'),
  ('importlib._abc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\_abc.py',
   'PYMODULE-1'),
  ('importlib._bootstrap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-1'),
  ('importlib._bootstrap_external',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-1'),
  ('importlib.abc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\abc.py',
   'PYMODULE-1'),
  ('importlib.machinery',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\machinery.py',
   'PYMODULE-1'),
  ('importlib.metadata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-1'),
  ('importlib.metadata._adapters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.metadata._collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-1'),
  ('importlib.metadata._functools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-1'),
  ('importlib.metadata._itertools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.metadata._meta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-1'),
  ('importlib.metadata._text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-1'),
  ('importlib.readers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\readers.py',
   'PYMODULE-1'),
  ('importlib.resources',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-1'),
  ('importlib.resources._adapters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.resources._common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-1'),
  ('importlib.resources._itertools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.resources._legacy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-1'),
  ('importlib.resources.abc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-1'),
  ('importlib.resources.readers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-1'),
  ('importlib.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\importlib\\util.py',
   'PYMODULE-1'),
  ('inspect',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\inspect.py',
   'PYMODULE-1'),
  ('ipaddress',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ipaddress.py',
   'PYMODULE-1'),
  ('jaraco', '-', 'PYMODULE-1'),
  ('jaraco.collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco\\collections\\__init__.py',
   'PYMODULE-1'),
  ('jaraco.context',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco\\context\\__init__.py',
   'PYMODULE-1'),
  ('jaraco.functools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco\\functools\\__init__.py',
   'PYMODULE-1'),
  ('jaraco.text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco\\text\\__init__.py',
   'PYMODULE-1'),
  ('jinja2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE-1'),
  ('jinja2._identifier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE-1'),
  ('jinja2.async_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE-1'),
  ('jinja2.bccache',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE-1'),
  ('jinja2.compiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE-1'),
  ('jinja2.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE-1'),
  ('jinja2.debug',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE-1'),
  ('jinja2.defaults',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE-1'),
  ('jinja2.environment',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE-1'),
  ('jinja2.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE-1'),
  ('jinja2.ext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE-1'),
  ('jinja2.filters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE-1'),
  ('jinja2.idtracking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE-1'),
  ('jinja2.lexer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE-1'),
  ('jinja2.loaders',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE-1'),
  ('jinja2.nodes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE-1'),
  ('jinja2.optimizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE-1'),
  ('jinja2.parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE-1'),
  ('jinja2.runtime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE-1'),
  ('jinja2.sandbox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE-1'),
  ('jinja2.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE-1'),
  ('jinja2.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE-1'),
  ('jinja2.visitor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE-1'),
  ('joblib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\__init__.py',
   'PYMODULE-1'),
  ('joblib._cloudpickle_wrapper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_cloudpickle_wrapper.py',
   'PYMODULE-1'),
  ('joblib._dask',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_dask.py',
   'PYMODULE-1'),
  ('joblib._memmapping_reducer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_memmapping_reducer.py',
   'PYMODULE-1'),
  ('joblib._multiprocessing_helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_multiprocessing_helpers.py',
   'PYMODULE-1'),
  ('joblib._parallel_backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_parallel_backends.py',
   'PYMODULE-1'),
  ('joblib._store_backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_store_backends.py',
   'PYMODULE-1'),
  ('joblib._utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\_utils.py',
   'PYMODULE-1'),
  ('joblib.backports',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\backports.py',
   'PYMODULE-1'),
  ('joblib.compressor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\compressor.py',
   'PYMODULE-1'),
  ('joblib.disk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\disk.py',
   'PYMODULE-1'),
  ('joblib.executor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\executor.py',
   'PYMODULE-1'),
  ('joblib.externals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\__init__.py',
   'PYMODULE-1'),
  ('joblib.externals.cloudpickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\cloudpickle\\__init__.py',
   'PYMODULE-1'),
  ('joblib.externals.cloudpickle.cloudpickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\cloudpickle\\cloudpickle.py',
   'PYMODULE-1'),
  ('joblib.externals.loky',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\__init__.py',
   'PYMODULE-1'),
  ('joblib.externals.loky._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\_base.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\__init__.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend._posix_reduction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\_posix_reduction.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend._win_reduction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\_win_reduction.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.context',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\context.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.fork_exec',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\fork_exec.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.popen_loky_posix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_posix.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.popen_loky_win32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_win32.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.process',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\process.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.queues',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\queues.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.reduction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\reduction.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.resource_tracker',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\resource_tracker.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.spawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\spawn.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.synchronize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\synchronize.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.backend.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\utils.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.cloudpickle_wrapper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\cloudpickle_wrapper.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.initializers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\initializers.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.process_executor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py',
   'PYMODULE-1'),
  ('joblib.externals.loky.reusable_executor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\externals\\loky\\reusable_executor.py',
   'PYMODULE-1'),
  ('joblib.func_inspect',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\func_inspect.py',
   'PYMODULE-1'),
  ('joblib.hashing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\hashing.py',
   'PYMODULE-1'),
  ('joblib.logger',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\logger.py',
   'PYMODULE-1'),
  ('joblib.memory',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\memory.py',
   'PYMODULE-1'),
  ('joblib.numpy_pickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\numpy_pickle.py',
   'PYMODULE-1'),
  ('joblib.numpy_pickle_compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\numpy_pickle_compat.py',
   'PYMODULE-1'),
  ('joblib.numpy_pickle_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\numpy_pickle_utils.py',
   'PYMODULE-1'),
  ('joblib.parallel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\parallel.py',
   'PYMODULE-1'),
  ('joblib.pool',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\joblib\\pool.py',
   'PYMODULE-1'),
  ('json',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\json\\__init__.py',
   'PYMODULE-1'),
  ('json.decoder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\json\\decoder.py',
   'PYMODULE-1'),
  ('json.encoder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\json\\encoder.py',
   'PYMODULE-1'),
  ('json.scanner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\json\\scanner.py',
   'PYMODULE-1'),
  ('kiwisolver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE-1'),
  ('kiwisolver.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE-1'),
  ('logging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\logging\\__init__.py',
   'PYMODULE-1'),
  ('lzma',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\lzma.py',
   'PYMODULE-1'),
  ('markupsafe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE-1'),
  ('markupsafe._native',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE-1'),
  ('matplotlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib._afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE-1'),
  ('matplotlib._animation_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_animation_data.py',
   'PYMODULE-1'),
  ('matplotlib._api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib._api.deprecation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE-1'),
  ('matplotlib._blocking_input',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE-1'),
  ('matplotlib._cm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE-1'),
  ('matplotlib._cm_listed',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE-1'),
  ('matplotlib._color_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE-1'),
  ('matplotlib._constrained_layout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE-1'),
  ('matplotlib._docstring',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE-1'),
  ('matplotlib._enums',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE-1'),
  ('matplotlib._fontconfig_pattern',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE-1'),
  ('matplotlib._layoutgrid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE-1'),
  ('matplotlib._mathtext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE-1'),
  ('matplotlib._mathtext_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE-1'),
  ('matplotlib._pylab_helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE-1'),
  ('matplotlib._text_helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE-1'),
  ('matplotlib._tight_bbox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE-1'),
  ('matplotlib._tight_layout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE-1'),
  ('matplotlib._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE-1'),
  ('matplotlib.animation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\animation.py',
   'PYMODULE-1'),
  ('matplotlib.artist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE-1'),
  ('matplotlib.axes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.axes._axes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE-1'),
  ('matplotlib.axes._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE-1'),
  ('matplotlib.axes._secondary_axes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE-1'),
  ('matplotlib.axis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE-1'),
  ('matplotlib.backend_bases',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE-1'),
  ('matplotlib.backend_managers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE-1'),
  ('matplotlib.backend_tools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE-1'),
  ('matplotlib.backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_agg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_qt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_qt5agg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_qt5agg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_qtagg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_webagg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE-1'),
  ('matplotlib.backends.backend_webagg_core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE-1'),
  ('matplotlib.backends.qt_compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE-1'),
  ('matplotlib.backends.qt_editor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.backends.qt_editor._formlayout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE-1'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE-1'),
  ('matplotlib.bezier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE-1'),
  ('matplotlib.category',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE-1'),
  ('matplotlib.cbook',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE-1'),
  ('matplotlib.cm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE-1'),
  ('matplotlib.collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE-1'),
  ('matplotlib.colorbar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE-1'),
  ('matplotlib.colors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE-1'),
  ('matplotlib.container',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE-1'),
  ('matplotlib.contour',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE-1'),
  ('matplotlib.dates',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE-1'),
  ('matplotlib.dviread',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE-1'),
  ('matplotlib.figure',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE-1'),
  ('matplotlib.font_manager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE-1'),
  ('matplotlib.gridspec',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE-1'),
  ('matplotlib.hatch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE-1'),
  ('matplotlib.image',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE-1'),
  ('matplotlib.layout_engine',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE-1'),
  ('matplotlib.legend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE-1'),
  ('matplotlib.legend_handler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE-1'),
  ('matplotlib.lines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE-1'),
  ('matplotlib.markers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE-1'),
  ('matplotlib.mathtext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE-1'),
  ('matplotlib.mlab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE-1'),
  ('matplotlib.offsetbox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE-1'),
  ('matplotlib.patches',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE-1'),
  ('matplotlib.path',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE-1'),
  ('matplotlib.patheffects',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE-1'),
  ('matplotlib.projections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.projections.geo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE-1'),
  ('matplotlib.projections.polar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE-1'),
  ('matplotlib.pylab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE-1'),
  ('matplotlib.pyplot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE-1'),
  ('matplotlib.quiver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE-1'),
  ('matplotlib.rcsetup',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE-1'),
  ('matplotlib.scale',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE-1'),
  ('matplotlib.spines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE-1'),
  ('matplotlib.stackplot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE-1'),
  ('matplotlib.streamplot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE-1'),
  ('matplotlib.style',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.style.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE-1'),
  ('matplotlib.table',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE-1'),
  ('matplotlib.texmanager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE-1'),
  ('matplotlib.text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE-1'),
  ('matplotlib.textpath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE-1'),
  ('matplotlib.ticker',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE-1'),
  ('matplotlib.transforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE-1'),
  ('matplotlib.tri',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triangulation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tricontour',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE-1'),
  ('matplotlib.tri._trifinder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triinterpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tripcolor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE-1'),
  ('matplotlib.tri._triplot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE-1'),
  ('matplotlib.tri._trirefine',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE-1'),
  ('matplotlib.tri._tritools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE-1'),
  ('matplotlib.typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE-1'),
  ('matplotlib.units',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE-1'),
  ('matplotlib.widgets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE-1'),
  ('mimetypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\mimetypes.py',
   'PYMODULE-1'),
  ('mne',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\__init__.py',
   'PYMODULE-1'),
  ('mne._freesurfer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\_freesurfer.py',
   'PYMODULE-1'),
  ('mne._ola',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\_ola.py',
   'PYMODULE-1'),
  ('mne._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\_version.py',
   'PYMODULE-1'),
  ('mne.annotations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\annotations.py',
   'PYMODULE-1'),
  ('mne.baseline',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\baseline.py',
   'PYMODULE-1'),
  ('mne.beamformer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\__init__.py',
   'PYMODULE-1'),
  ('mne.beamformer._compute_beamformer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\_compute_beamformer.py',
   'PYMODULE-1'),
  ('mne.beamformer._dics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\_dics.py',
   'PYMODULE-1'),
  ('mne.beamformer._lcmv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\_lcmv.py',
   'PYMODULE-1'),
  ('mne.beamformer._rap_music',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\_rap_music.py',
   'PYMODULE-1'),
  ('mne.beamformer.resolution_matrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\beamformer\\resolution_matrix.py',
   'PYMODULE-1'),
  ('mne.bem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\bem.py',
   'PYMODULE-1'),
  ('mne.channels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\__init__.py',
   'PYMODULE-1'),
  ('mne.channels._dig_montage_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\_dig_montage_utils.py',
   'PYMODULE-1'),
  ('mne.channels._standard_montage_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\_standard_montage_utils.py',
   'PYMODULE-1'),
  ('mne.channels.channels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\channels.py',
   'PYMODULE-1'),
  ('mne.channels.interpolation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\interpolation.py',
   'PYMODULE-1'),
  ('mne.channels.layout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\layout.py',
   'PYMODULE-1'),
  ('mne.channels.montage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\montage.py',
   'PYMODULE-1'),
  ('mne.channels.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.channels.tests.test_channels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\test_channels.py',
   'PYMODULE-1'),
  ('mne.channels.tests.test_interpolation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\test_interpolation.py',
   'PYMODULE-1'),
  ('mne.channels.tests.test_layout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\test_layout.py',
   'PYMODULE-1'),
  ('mne.channels.tests.test_montage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\test_montage.py',
   'PYMODULE-1'),
  ('mne.channels.tests.test_standard_montage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\tests\\test_standard_montage.py',
   'PYMODULE-1'),
  ('mne.chpi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\chpi.py',
   'PYMODULE-1'),
  ('mne.coreg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\coreg.py',
   'PYMODULE-1'),
  ('mne.cov',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\cov.py',
   'PYMODULE-1'),
  ('mne.cuda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\cuda.py',
   'PYMODULE-1'),
  ('mne.decoding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\__init__.py',
   'PYMODULE-1'),
  ('mne.decoding.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\base.py',
   'PYMODULE-1'),
  ('mne.decoding.csp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\csp.py',
   'PYMODULE-1'),
  ('mne.decoding.ems',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\ems.py',
   'PYMODULE-1'),
  ('mne.decoding.mixin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\mixin.py',
   'PYMODULE-1'),
  ('mne.decoding.receptive_field',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\receptive_field.py',
   'PYMODULE-1'),
  ('mne.decoding.search_light',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\search_light.py',
   'PYMODULE-1'),
  ('mne.decoding.ssd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\ssd.py',
   'PYMODULE-1'),
  ('mne.decoding.time_delaying_ridge',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\time_delaying_ridge.py',
   'PYMODULE-1'),
  ('mne.decoding.time_frequency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\time_frequency.py',
   'PYMODULE-1'),
  ('mne.decoding.transformer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\decoding\\transformer.py',
   'PYMODULE-1'),
  ('mne.defaults',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\defaults.py',
   'PYMODULE-1'),
  ('mne.dipole',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\dipole.py',
   'PYMODULE-1'),
  ('mne.epochs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\epochs.py',
   'PYMODULE-1'),
  ('mne.event',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\event.py',
   'PYMODULE-1'),
  ('mne.evoked',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\evoked.py',
   'PYMODULE-1'),
  ('mne.export',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\__init__.py',
   'PYMODULE-1'),
  ('mne.export._brainvision',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\_brainvision.py',
   'PYMODULE-1'),
  ('mne.export._edf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\_edf.py',
   'PYMODULE-1'),
  ('mne.export._eeglab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\_eeglab.py',
   'PYMODULE-1'),
  ('mne.export._egimff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\_egimff.py',
   'PYMODULE-1'),
  ('mne.export._export',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\export\\_export.py',
   'PYMODULE-1'),
  ('mne.filter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\filter.py',
   'PYMODULE-1'),
  ('mne.fixes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\fixes.py',
   'PYMODULE-1'),
  ('mne.forward',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\__init__.py',
   'PYMODULE-1'),
  ('mne.forward._compute_forward',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\_compute_forward.py',
   'PYMODULE-1'),
  ('mne.forward._field_interpolation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\_field_interpolation.py',
   'PYMODULE-1'),
  ('mne.forward._lead_dots',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\_lead_dots.py',
   'PYMODULE-1'),
  ('mne.forward._make_forward',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\_make_forward.py',
   'PYMODULE-1'),
  ('mne.forward.forward',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\forward\\forward.py',
   'PYMODULE-1'),
  ('mne.html_templates',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\html_templates\\__init__.py',
   'PYMODULE-1'),
  ('mne.html_templates._templates',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\html_templates\\_templates.py',
   'PYMODULE-1'),
  ('mne.inverse_sparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\inverse_sparse\\__init__.py',
   'PYMODULE-1'),
  ('mne.inverse_sparse._gamma_map',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\inverse_sparse\\_gamma_map.py',
   'PYMODULE-1'),
  ('mne.inverse_sparse.mxne_debiasing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\inverse_sparse\\mxne_debiasing.py',
   'PYMODULE-1'),
  ('mne.inverse_sparse.mxne_inverse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\inverse_sparse\\mxne_inverse.py',
   'PYMODULE-1'),
  ('mne.inverse_sparse.mxne_optim',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\inverse_sparse\\mxne_optim.py',
   'PYMODULE-1'),
  ('mne.io',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\__init__.py',
   'PYMODULE-1'),
  ('mne.io._digitization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\_digitization.py',
   'PYMODULE-1'),
  ('mne.io._read_raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\_read_raw.py',
   'PYMODULE-1'),
  ('mne.io.array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\array\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.array.array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\array\\array.py',
   'PYMODULE-1'),
  ('mne.io.array.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\array\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.array.tests.test_array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\array\\tests\\test_array.py',
   'PYMODULE-1'),
  ('mne.io.artemis123',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\artemis123\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.artemis123.artemis123',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\artemis123\\artemis123.py',
   'PYMODULE-1'),
  ('mne.io.artemis123.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\artemis123\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.artemis123.tests.test_artemis123',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\artemis123\\tests\\test_artemis123.py',
   'PYMODULE-1'),
  ('mne.io.artemis123.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\artemis123\\utils.py',
   'PYMODULE-1'),
  ('mne.io.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\base.py',
   'PYMODULE-1'),
  ('mne.io.besa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\besa\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.besa.besa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\besa\\besa.py',
   'PYMODULE-1'),
  ('mne.io.boxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\boxy\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.boxy.boxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\boxy\\boxy.py',
   'PYMODULE-1'),
  ('mne.io.boxy.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\boxy\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.boxy.tests.test_boxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\boxy\\tests\\test_boxy.py',
   'PYMODULE-1'),
  ('mne.io.brainvision',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\brainvision\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.brainvision.brainvision',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\brainvision\\brainvision.py',
   'PYMODULE-1'),
  ('mne.io.brainvision.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\brainvision\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.brainvision.tests.test_brainvision',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\brainvision\\tests\\test_brainvision.py',
   'PYMODULE-1'),
  ('mne.io.bti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.bti.bti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\bti.py',
   'PYMODULE-1'),
  ('mne.io.bti.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\constants.py',
   'PYMODULE-1'),
  ('mne.io.bti.read',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\read.py',
   'PYMODULE-1'),
  ('mne.io.bti.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.bti.tests.test_bti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\bti\\tests\\test_bti.py',
   'PYMODULE-1'),
  ('mne.io.cnt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\cnt\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.cnt._utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\cnt\\_utils.py',
   'PYMODULE-1'),
  ('mne.io.cnt.cnt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\cnt\\cnt.py',
   'PYMODULE-1'),
  ('mne.io.cnt.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\cnt\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.cnt.tests.test_cnt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\cnt\\tests\\test_cnt.py',
   'PYMODULE-1'),
  ('mne.io.compensator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\compensator.py',
   'PYMODULE-1'),
  ('mne.io.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\constants.py',
   'PYMODULE-1'),
  ('mne.io.ctf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.ctf.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\constants.py',
   'PYMODULE-1'),
  ('mne.io.ctf.ctf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\ctf.py',
   'PYMODULE-1'),
  ('mne.io.ctf.eeg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\eeg.py',
   'PYMODULE-1'),
  ('mne.io.ctf.hc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\hc.py',
   'PYMODULE-1'),
  ('mne.io.ctf.info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\info.py',
   'PYMODULE-1'),
  ('mne.io.ctf.markers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\markers.py',
   'PYMODULE-1'),
  ('mne.io.ctf.res4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\res4.py',
   'PYMODULE-1'),
  ('mne.io.ctf.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.ctf.tests.test_ctf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\tests\\test_ctf.py',
   'PYMODULE-1'),
  ('mne.io.ctf.trans',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf\\trans.py',
   'PYMODULE-1'),
  ('mne.io.ctf_comp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\ctf_comp.py',
   'PYMODULE-1'),
  ('mne.io.curry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\curry\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.curry.curry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\curry\\curry.py',
   'PYMODULE-1'),
  ('mne.io.curry.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\curry\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.curry.tests.test_curry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\curry\\tests\\test_curry.py',
   'PYMODULE-1'),
  ('mne.io.diff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\diff.py',
   'PYMODULE-1'),
  ('mne.io.edf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\edf\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.edf.edf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\edf\\edf.py',
   'PYMODULE-1'),
  ('mne.io.edf.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\edf\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.edf.tests.test_edf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\edf\\tests\\test_edf.py',
   'PYMODULE-1'),
  ('mne.io.edf.tests.test_gdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\edf\\tests\\test_gdf.py',
   'PYMODULE-1'),
  ('mne.io.eeglab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eeglab\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eeglab._eeglab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eeglab\\_eeglab.py',
   'PYMODULE-1'),
  ('mne.io.eeglab.eeglab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eeglab\\eeglab.py',
   'PYMODULE-1'),
  ('mne.io.eeglab.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eeglab\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eeglab.tests.test_eeglab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eeglab\\tests\\test_eeglab.py',
   'PYMODULE-1'),
  ('mne.io.egi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.egi.egi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\egi.py',
   'PYMODULE-1'),
  ('mne.io.egi.egimff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\egimff.py',
   'PYMODULE-1'),
  ('mne.io.egi.events',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\events.py',
   'PYMODULE-1'),
  ('mne.io.egi.general',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\general.py',
   'PYMODULE-1'),
  ('mne.io.egi.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.egi.tests.test_egi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\egi\\tests\\test_egi.py',
   'PYMODULE-1'),
  ('mne.io.eximia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eximia\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eximia.eximia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eximia\\eximia.py',
   'PYMODULE-1'),
  ('mne.io.eximia.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eximia\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eximia.tests.test_eximia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eximia\\tests\\test_eximia.py',
   'PYMODULE-1'),
  ('mne.io.eyelink',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eyelink\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eyelink._utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eyelink\\_utils.py',
   'PYMODULE-1'),
  ('mne.io.eyelink.eyelink',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eyelink\\eyelink.py',
   'PYMODULE-1'),
  ('mne.io.eyelink.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eyelink\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.eyelink.tests.test_eyelink',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\eyelink\\tests\\test_eyelink.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip.fieldtrip',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\fieldtrip.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip.tests.helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\tests\\helpers.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip.tests.test_fieldtrip',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\tests\\test_fieldtrip.py',
   'PYMODULE-1'),
  ('mne.io.fieldtrip.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fieldtrip\\utils.py',
   'PYMODULE-1'),
  ('mne.io.fiff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fiff\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.fiff.raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fiff\\raw.py',
   'PYMODULE-1'),
  ('mne.io.fiff.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fiff\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.fiff.tests.test_raw_fiff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fiff\\tests\\test_raw_fiff.py',
   'PYMODULE-1'),
  ('mne.io.fil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fil\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.fil.fil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fil\\fil.py',
   'PYMODULE-1'),
  ('mne.io.fil.sensors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\fil\\sensors.py',
   'PYMODULE-1'),
  ('mne.io.hitachi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\hitachi\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.hitachi.hitachi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\hitachi\\hitachi.py',
   'PYMODULE-1'),
  ('mne.io.kit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.kit.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\constants.py',
   'PYMODULE-1'),
  ('mne.io.kit.coreg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\coreg.py',
   'PYMODULE-1'),
  ('mne.io.kit.kit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\kit.py',
   'PYMODULE-1'),
  ('mne.io.kit.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.kit.tests.test_coreg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\tests\\test_coreg.py',
   'PYMODULE-1'),
  ('mne.io.kit.tests.test_kit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\kit\\tests\\test_kit.py',
   'PYMODULE-1'),
  ('mne.io.matrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\matrix.py',
   'PYMODULE-1'),
  ('mne.io.meas_info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\meas_info.py',
   'PYMODULE-1'),
  ('mne.io.nedf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nedf\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nedf.nedf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nedf\\nedf.py',
   'PYMODULE-1'),
  ('mne.io.nedf.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nedf\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nedf.tests.test_nedf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nedf\\tests\\test_nedf.py',
   'PYMODULE-1'),
  ('mne.io.nicolet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nicolet\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nicolet.nicolet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nicolet\\nicolet.py',
   'PYMODULE-1'),
  ('mne.io.nicolet.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nicolet\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nicolet.tests.test_nicolet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nicolet\\tests\\test_nicolet.py',
   'PYMODULE-1'),
  ('mne.io.nihon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nihon\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nihon.nihon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nihon\\nihon.py',
   'PYMODULE-1'),
  ('mne.io.nirx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nirx\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nirx._localized_abbr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nirx\\_localized_abbr.py',
   'PYMODULE-1'),
  ('mne.io.nirx.nirx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nirx\\nirx.py',
   'PYMODULE-1'),
  ('mne.io.nirx.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nirx\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nirx.tests.test_nirx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nirx\\tests\\test_nirx.py',
   'PYMODULE-1'),
  ('mne.io.nsx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nsx\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.nsx.nsx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\nsx\\nsx.py',
   'PYMODULE-1'),
  ('mne.io.open',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\open.py',
   'PYMODULE-1'),
  ('mne.io.persyst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\persyst\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.persyst.persyst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\persyst\\persyst.py',
   'PYMODULE-1'),
  ('mne.io.persyst.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\persyst\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.persyst.tests.test_persyst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\persyst\\tests\\test_persyst.py',
   'PYMODULE-1'),
  ('mne.io.pick',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\pick.py',
   'PYMODULE-1'),
  ('mne.io.proc_history',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\proc_history.py',
   'PYMODULE-1'),
  ('mne.io.proj',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\proj.py',
   'PYMODULE-1'),
  ('mne.io.reference',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\reference.py',
   'PYMODULE-1'),
  ('mne.io.snirf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\snirf\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.snirf._snirf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\snirf\\_snirf.py',
   'PYMODULE-1'),
  ('mne.io.snirf.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\snirf\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.snirf.tests.test_snirf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\snirf\\tests\\test_snirf.py',
   'PYMODULE-1'),
  ('mne.io.tag',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tag.py',
   'PYMODULE-1'),
  ('mne.io.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.tests.data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\data\\__init__.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_apply_function',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_apply_function.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_compensator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_compensator.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_constants.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_meas_info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_meas_info.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_pick',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_pick.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_proc_history',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_proc_history.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_raw.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_read_raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_read_raw.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_reference',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_reference.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_show_fiff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_show_fiff.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_utils.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_what',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_what.py',
   'PYMODULE-1'),
  ('mne.io.tests.test_write',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tests\\test_write.py',
   'PYMODULE-1'),
  ('mne.io.tree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\tree.py',
   'PYMODULE-1'),
  ('mne.io.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\utils.py',
   'PYMODULE-1'),
  ('mne.io.what',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\what.py',
   'PYMODULE-1'),
  ('mne.io.write',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\io\\write.py',
   'PYMODULE-1'),
  ('mne.label',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\label.py',
   'PYMODULE-1'),
  ('mne.minimum_norm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\__init__.py',
   'PYMODULE-1'),
  ('mne.minimum_norm._eloreta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\_eloreta.py',
   'PYMODULE-1'),
  ('mne.minimum_norm.inverse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\inverse.py',
   'PYMODULE-1'),
  ('mne.minimum_norm.resolution_matrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\resolution_matrix.py',
   'PYMODULE-1'),
  ('mne.minimum_norm.spatial_resolution',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\spatial_resolution.py',
   'PYMODULE-1'),
  ('mne.minimum_norm.time_frequency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\minimum_norm\\time_frequency.py',
   'PYMODULE-1'),
  ('mne.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\misc.py',
   'PYMODULE-1'),
  ('mne.morph',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\morph.py',
   'PYMODULE-1'),
  ('mne.morph_map',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\morph_map.py',
   'PYMODULE-1'),
  ('mne.parallel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\parallel.py',
   'PYMODULE-1'),
  ('mne.preprocessing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing._csd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\_csd.py',
   'PYMODULE-1'),
  ('mne.preprocessing._css',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\_css.py',
   'PYMODULE-1'),
  ('mne.preprocessing._fine_cal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\_fine_cal.py',
   'PYMODULE-1'),
  ('mne.preprocessing._peak_finder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\_peak_finder.py',
   'PYMODULE-1'),
  ('mne.preprocessing._regress',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\_regress.py',
   'PYMODULE-1'),
  ('mne.preprocessing.annotate_amplitude',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\annotate_amplitude.py',
   'PYMODULE-1'),
  ('mne.preprocessing.annotate_nan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\annotate_nan.py',
   'PYMODULE-1'),
  ('mne.preprocessing.artifact_detection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\artifact_detection.py',
   'PYMODULE-1'),
  ('mne.preprocessing.bads',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\bads.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ctps_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ctps_.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ecg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ecg.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eog.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking._pupillometry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\_pupillometry.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking.calibration',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\calibration.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking.eyetracking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\eyetracking.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking.tests.test_calibration',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\tests\\test_calibration.py',
   'PYMODULE-1'),
  ('mne.preprocessing.eyetracking.tests.test_pupillometry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\eyetracking\\tests\\test_pupillometry.py',
   'PYMODULE-1'),
  ('mne.preprocessing.hfc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\hfc.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ica.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ieeg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ieeg\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ieeg._projection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ieeg\\_projection.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ieeg._volume',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ieeg\\_volume.py',
   'PYMODULE-1'),
  ('mne.preprocessing.infomax_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\infomax_.py',
   'PYMODULE-1'),
  ('mne.preprocessing.interpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\interpolate.py',
   'PYMODULE-1'),
  ('mne.preprocessing.maxfilter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\maxfilter.py',
   'PYMODULE-1'),
  ('mne.preprocessing.maxwell',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\maxwell.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs._beer_lambert_law',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\_beer_lambert_law.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs._optical_density',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\_optical_density.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs._scalp_coupling_index',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\_scalp_coupling_index.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs._tddr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\_tddr.py',
   'PYMODULE-1'),
  ('mne.preprocessing.nirs.nirs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\nirs\\nirs.py',
   'PYMODULE-1'),
  ('mne.preprocessing.otp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\otp.py',
   'PYMODULE-1'),
  ('mne.preprocessing.realign',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\realign.py',
   'PYMODULE-1'),
  ('mne.preprocessing.ssp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\ssp.py',
   'PYMODULE-1'),
  ('mne.preprocessing.stim',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\stim.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\__init__.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_annotate_amplitude',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_annotate_amplitude.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_annotate_nan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_annotate_nan.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_artifact_detection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_artifact_detection.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_csd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_csd.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_css',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_css.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_ctps',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_ctps.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_ecg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_ecg.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_eeglab_infomax',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_eeglab_infomax.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_eog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_eog.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_fine_cal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_fine_cal.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_hfc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_hfc.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_ica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_ica.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_infomax',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_infomax.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_interpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_interpolate.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_maxwell',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_maxwell.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_otp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_otp.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_peak_finder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_peak_finder.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_realign',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_realign.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_regress',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_regress.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_ssp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_ssp.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_stim',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_stim.py',
   'PYMODULE-1'),
  ('mne.preprocessing.tests.test_xdawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\tests\\test_xdawn.py',
   'PYMODULE-1'),
  ('mne.preprocessing.xdawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\preprocessing\\xdawn.py',
   'PYMODULE-1'),
  ('mne.proj',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\proj.py',
   'PYMODULE-1'),
  ('mne.rank',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\rank.py',
   'PYMODULE-1'),
  ('mne.report',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\report\\__init__.py',
   'PYMODULE-1'),
  ('mne.report.report',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\report\\report.py',
   'PYMODULE-1'),
  ('mne.simulation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\__init__.py',
   'PYMODULE-1'),
  ('mne.simulation.evoked',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\evoked.py',
   'PYMODULE-1'),
  ('mne.simulation.metrics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\metrics\\__init__.py',
   'PYMODULE-1'),
  ('mne.simulation.metrics.metrics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\metrics\\metrics.py',
   'PYMODULE-1'),
  ('mne.simulation.raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\raw.py',
   'PYMODULE-1'),
  ('mne.simulation.source',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\simulation\\source.py',
   'PYMODULE-1'),
  ('mne.source_estimate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\source_estimate.py',
   'PYMODULE-1'),
  ('mne.source_space',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\source_space.py',
   'PYMODULE-1'),
  ('mne.stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\__init__.py',
   'PYMODULE-1'),
  ('mne.stats._adjacency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\_adjacency.py',
   'PYMODULE-1'),
  ('mne.stats.cluster_level',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\cluster_level.py',
   'PYMODULE-1'),
  ('mne.stats.multi_comp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\multi_comp.py',
   'PYMODULE-1'),
  ('mne.stats.parametric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\parametric.py',
   'PYMODULE-1'),
  ('mne.stats.permutations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\permutations.py',
   'PYMODULE-1'),
  ('mne.stats.regression',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\stats\\regression.py',
   'PYMODULE-1'),
  ('mne.surface',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\surface.py',
   'PYMODULE-1'),
  ('mne.time_frequency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\__init__.py',
   'PYMODULE-1'),
  ('mne.time_frequency._stft',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\_stft.py',
   'PYMODULE-1'),
  ('mne.time_frequency._stockwell',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\_stockwell.py',
   'PYMODULE-1'),
  ('mne.time_frequency.ar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\ar.py',
   'PYMODULE-1'),
  ('mne.time_frequency.csd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\csd.py',
   'PYMODULE-1'),
  ('mne.time_frequency.multitaper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\multitaper.py',
   'PYMODULE-1'),
  ('mne.time_frequency.psd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\psd.py',
   'PYMODULE-1'),
  ('mne.time_frequency.spectrum',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\spectrum.py',
   'PYMODULE-1'),
  ('mne.time_frequency.tfr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\time_frequency\\tfr.py',
   'PYMODULE-1'),
  ('mne.transforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\transforms.py',
   'PYMODULE-1'),
  ('mne.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\__init__.py',
   'PYMODULE-1'),
  ('mne.utils._bunch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\_bunch.py',
   'PYMODULE-1'),
  ('mne.utils._logging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\_logging.py',
   'PYMODULE-1'),
  ('mne.utils._testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\_testing.py',
   'PYMODULE-1'),
  ('mne.utils.check',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\check.py',
   'PYMODULE-1'),
  ('mne.utils.config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\config.py',
   'PYMODULE-1'),
  ('mne.utils.dataframe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\dataframe.py',
   'PYMODULE-1'),
  ('mne.utils.docs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\docs.py',
   'PYMODULE-1'),
  ('mne.utils.fetching',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\fetching.py',
   'PYMODULE-1'),
  ('mne.utils.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\linalg.py',
   'PYMODULE-1'),
  ('mne.utils.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\misc.py',
   'PYMODULE-1'),
  ('mne.utils.mixin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\mixin.py',
   'PYMODULE-1'),
  ('mne.utils.numerics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\numerics.py',
   'PYMODULE-1'),
  ('mne.utils.progressbar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\progressbar.py',
   'PYMODULE-1'),
  ('mne.utils.spectrum',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\utils\\spectrum.py',
   'PYMODULE-1'),
  ('mne.viz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\__init__.py',
   'PYMODULE-1'),
  ('mne.viz._3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_3d.py',
   'PYMODULE-1'),
  ('mne.viz._3d_overlay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_3d_overlay.py',
   'PYMODULE-1'),
  ('mne.viz._dipole',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_dipole.py',
   'PYMODULE-1'),
  ('mne.viz._figure',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_figure.py',
   'PYMODULE-1'),
  ('mne.viz._mpl_figure',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_mpl_figure.py',
   'PYMODULE-1'),
  ('mne.viz._proj',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_proj.py',
   'PYMODULE-1'),
  ('mne.viz._scraper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\_scraper.py',
   'PYMODULE-1'),
  ('mne.viz.backends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\backends\\__init__.py',
   'PYMODULE-1'),
  ('mne.viz.backends._abstract',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\backends\\_abstract.py',
   'PYMODULE-1'),
  ('mne.viz.backends._utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\backends\\_utils.py',
   'PYMODULE-1'),
  ('mne.viz.circle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\circle.py',
   'PYMODULE-1'),
  ('mne.viz.epochs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\epochs.py',
   'PYMODULE-1'),
  ('mne.viz.evoked',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\evoked.py',
   'PYMODULE-1'),
  ('mne.viz.ica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\ica.py',
   'PYMODULE-1'),
  ('mne.viz.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\misc.py',
   'PYMODULE-1'),
  ('mne.viz.montage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\montage.py',
   'PYMODULE-1'),
  ('mne.viz.raw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\raw.py',
   'PYMODULE-1'),
  ('mne.viz.topo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\topo.py',
   'PYMODULE-1'),
  ('mne.viz.topomap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\topomap.py',
   'PYMODULE-1'),
  ('mne.viz.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\viz\\utils.py',
   'PYMODULE-1'),
  ('more_itertools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE-1'),
  ('more_itertools.more',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE-1'),
  ('more_itertools.recipes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE-1'),
  ('mpl_toolkits',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\__init__.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\__init__.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.axes_divider',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\axes_divider.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.axes_grid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\axes_grid.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.axes_size',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\axes_size.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.inset_locator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\inset_locator.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.mpl_axes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\mpl_axes.py',
   'PYMODULE-1'),
  ('mpl_toolkits.axes_grid1.parasite_axes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\parasite_axes.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.art3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.axes3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.axis3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE-1'),
  ('mpl_toolkits.mplot3d.proj3d',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE-1'),
  ('multiprocessing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.context',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\context.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.forkserver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.heap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-1'),
  ('multiprocessing.managers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-1'),
  ('multiprocessing.pool',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_fork',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_forkserver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_posix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_win32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-1'),
  ('multiprocessing.process',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\process.py',
   'PYMODULE-1'),
  ('multiprocessing.queues',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-1'),
  ('multiprocessing.reduction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_sharer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_tracker',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-1'),
  ('multiprocessing.shared_memory',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-1'),
  ('multiprocessing.sharedctypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-1'),
  ('multiprocessing.spawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-1'),
  ('multiprocessing.synchronize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-1'),
  ('multiprocessing.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\multiprocessing\\util.py',
   'PYMODULE-1'),
  ('netrc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\netrc.py',
   'PYMODULE-1'),
  ('nturl2path',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\nturl2path.py',
   'PYMODULE-1'),
  ('numbers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\numbers.py',
   'PYMODULE-1'),
  ('numpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('numpy.__config__',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-1'),
  ('numpy._core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-1'),
  ('numpy._core.multiarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-1'),
  ('numpy._distributor_init',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-1'),
  ('numpy._globals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-1'),
  ('numpy._pytesttester',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-1'),
  ('numpy._typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy._typing._add_docstring',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-1'),
  ('numpy._typing._array_like',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-1'),
  ('numpy._typing._char_codes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-1'),
  ('numpy._typing._dtype_like',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-1'),
  ('numpy._typing._nbit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-1'),
  ('numpy._typing._nested_sequence',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-1'),
  ('numpy._typing._scalars',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-1'),
  ('numpy._typing._shape',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-1'),
  ('numpy._utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-1'),
  ('numpy._utils._convertions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-1'),
  ('numpy._utils._inspect',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-1'),
  ('numpy.array_api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE-1'),
  ('numpy.array_api._array_object',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE-1'),
  ('numpy.array_api._constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE-1'),
  ('numpy.array_api._creation_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._data_type_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._dtypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE-1'),
  ('numpy.array_api._elementwise_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._indexing_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._manipulation_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._searching_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._set_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._sorting_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._statistical_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api._typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE-1'),
  ('numpy.array_api._utility_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE-1'),
  ('numpy.array_api.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE-1'),
  ('numpy.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE-1'),
  ('numpy.compat.py3k',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE-1'),
  ('numpy.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-1'),
  ('numpy.core._add_newdocs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE-1'),
  ('numpy.core._add_newdocs_scalars',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE-1'),
  ('numpy.core._asarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE-1'),
  ('numpy.core._dtype',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE-1'),
  ('numpy.core._dtype_ctypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE-1'),
  ('numpy.core._exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE-1'),
  ('numpy.core._internal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE-1'),
  ('numpy.core._machar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE-1'),
  ('numpy.core._methods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE-1'),
  ('numpy.core._string_helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE-1'),
  ('numpy.core._type_aliases',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE-1'),
  ('numpy.core._ufunc_config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE-1'),
  ('numpy.core.arrayprint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE-1'),
  ('numpy.core.defchararray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE-1'),
  ('numpy.core.einsumfunc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE-1'),
  ('numpy.core.fromnumeric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE-1'),
  ('numpy.core.function_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE-1'),
  ('numpy.core.getlimits',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE-1'),
  ('numpy.core.memmap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE-1'),
  ('numpy.core.multiarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE-1'),
  ('numpy.core.numeric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE-1'),
  ('numpy.core.numerictypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE-1'),
  ('numpy.core.overrides',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE-1'),
  ('numpy.core.records',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE-1'),
  ('numpy.core.shape_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE-1'),
  ('numpy.core.umath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE-1'),
  ('numpy.ctypeslib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-1'),
  ('numpy.dtypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-1'),
  ('numpy.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-1'),
  ('numpy.fft',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-1'),
  ('numpy.fft._pocketfft',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-1'),
  ('numpy.fft.helper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-1'),
  ('numpy.lib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.lib._datasource',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-1'),
  ('numpy.lib._iotools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-1'),
  ('numpy.lib._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-1'),
  ('numpy.lib.arraypad',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE-1'),
  ('numpy.lib.arraysetops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE-1'),
  ('numpy.lib.arrayterator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE-1'),
  ('numpy.lib.format',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-1'),
  ('numpy.lib.function_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE-1'),
  ('numpy.lib.histograms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE-1'),
  ('numpy.lib.index_tricks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE-1'),
  ('numpy.lib.mixins',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-1'),
  ('numpy.lib.nanfunctions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE-1'),
  ('numpy.lib.npyio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-1'),
  ('numpy.lib.polynomial',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE-1'),
  ('numpy.lib.recfunctions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-1'),
  ('numpy.lib.scimath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-1'),
  ('numpy.lib.shape_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE-1'),
  ('numpy.lib.stride_tricks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-1'),
  ('numpy.lib.twodim_base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE-1'),
  ('numpy.lib.type_check',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE-1'),
  ('numpy.lib.ufunclike',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE-1'),
  ('numpy.lib.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE-1'),
  ('numpy.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-1'),
  ('numpy.linalg.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-1'),
  ('numpy.ma',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-1'),
  ('numpy.ma.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-1'),
  ('numpy.ma.extras',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-1'),
  ('numpy.ma.mrecords',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-1'),
  ('numpy.matrixlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.matrixlib.defmatrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-1'),
  ('numpy.polynomial',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-1'),
  ('numpy.polynomial._polybase',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-1'),
  ('numpy.polynomial.chebyshev',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite_e',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-1'),
  ('numpy.polynomial.laguerre',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.legendre',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polynomial',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polyutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-1'),
  ('numpy.random',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-1'),
  ('numpy.random._pickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-1'),
  ('numpy.testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private.extbuild',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-1'),
  ('numpy.testing._private.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-1'),
  ('numpy.testing.overrides',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-1'),
  ('numpy.typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-1'),
  ('opcode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\opcode.py',
   'PYMODULE-1'),
  ('optparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\optparse.py',
   'PYMODULE-1'),
  ('packaging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('packaging._elffile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE-1'),
  ('packaging._manylinux',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE-1'),
  ('packaging._musllinux',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE-1'),
  ('packaging._parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE-1'),
  ('packaging._structures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('packaging._tokenizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE-1'),
  ('packaging.licenses',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE-1'),
  ('packaging.licenses._spdx',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE-1'),
  ('packaging.markers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE-1'),
  ('packaging.metadata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE-1'),
  ('packaging.requirements',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE-1'),
  ('packaging.specifiers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE-1'),
  ('packaging.tags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE-1'),
  ('packaging.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE-1'),
  ('packaging.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-1'),
  ('pandas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config.config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-1'),
  ('pandas._config.dates',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-1'),
  ('pandas._config.display',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-1'),
  ('pandas._config.localization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-1'),
  ('pandas._libs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.tslibs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.window',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing._io',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-1'),
  ('pandas._testing._random',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE-1'),
  ('pandas._testing._warnings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-1'),
  ('pandas._testing.asserters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-1'),
  ('pandas._testing.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-1'),
  ('pandas._testing.contexts',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-1'),
  ('pandas._typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-1'),
  ('pandas._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-1'),
  ('pandas.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.extensions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.indexers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.interchange',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-1'),
  ('pandas.arrays',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat._constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-1'),
  ('pandas.compat._optional',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-1'),
  ('pandas.compat.compressors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy.function',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-1'),
  ('pandas.compat.pickle_compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-1'),
  ('pandas.compat.pyarrow',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-1'),
  ('pandas.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.executor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.mean_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.min_max_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.shared',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.sum_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.var_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-1'),
  ('pandas.core.accessor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.algorithms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-1'),
  ('pandas.core.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-1'),
  ('pandas.core.apply',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_accumulations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_reductions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.putmask',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.quantile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.replace',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.take',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.transforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-1'),
  ('pandas.core.arraylike',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._mixins',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._ranges',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.dtype',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.extension_types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.boolean',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.categorical',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimelike',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.floating',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.integer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.interval',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.masked',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numeric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numpy_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.period',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.accessor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.dtype',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_arrow',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.timedeltas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-1'),
  ('pandas.core.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.computation.align',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-1'),
  ('pandas.core.computation.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-1'),
  ('pandas.core.computation.check',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-1'),
  ('pandas.core.computation.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation.engines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-1'),
  ('pandas.core.computation.eval',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expressions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-1'),
  ('pandas.core.computation.ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.computation.parsing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-1'),
  ('pandas.core.computation.pytables',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-1'),
  ('pandas.core.computation.scope',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-1'),
  ('pandas.core.config_init',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-1'),
  ('pandas.core.construction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.astype',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.cast',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.concat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.dtypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.inference',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.missing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.flags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-1'),
  ('pandas.core.frame',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-1'),
  ('pandas.core.generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.categorical',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.groupby',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.grouper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.indexing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.numba_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.indexers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.objects',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.indexes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.accessors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.category',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimelike',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.extension',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.frozen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.interval',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.multi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.period',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.range',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.timedeltas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.indexing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.interchange',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.buffer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.column',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe_protocol',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.from_dataframe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.internals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.internals.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-1'),
  ('pandas.core.internals.array_manager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-1'),
  ('pandas.core.internals.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-1'),
  ('pandas.core.internals.blocks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-1'),
  ('pandas.core.internals.concat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.internals.construction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.internals.managers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-1'),
  ('pandas.core.internals.ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.methods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.methods.describe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-1'),
  ('pandas.core.methods.selectn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-1'),
  ('pandas.core.methods.to_dict',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-1'),
  ('pandas.core.missing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.nanops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-1'),
  ('pandas.core.ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.ops.array_ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-1'),
  ('pandas.core.ops.dispatch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-1'),
  ('pandas.core.ops.docstrings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-1'),
  ('pandas.core.ops.invalid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-1'),
  ('pandas.core.ops.mask_ops',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.methods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE-1'),
  ('pandas.core.ops.missing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.resample',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-1'),
  ('pandas.core.reshape',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.concat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.encoding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.melt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.merge',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.pivot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.reshape',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.tile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-1'),
  ('pandas.core.roperator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-1'),
  ('pandas.core.sample',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-1'),
  ('pandas.core.series',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-1'),
  ('pandas.core.shared_docs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-1'),
  ('pandas.core.sorting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-1'),
  ('pandas.core.strings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.strings.accessor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.strings.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-1'),
  ('pandas.core.strings.object_array',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-1'),
  ('pandas.core.tools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.tools.datetimes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.tools.numeric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.tools.timedeltas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.tools.times',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-1'),
  ('pandas.core.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.util.hashing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-1'),
  ('pandas.core.util.numba_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.window.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-1'),
  ('pandas.core.window.doc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-1'),
  ('pandas.core.window.ewm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-1'),
  ('pandas.core.window.expanding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-1'),
  ('pandas.core.window.numba_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window.online',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-1'),
  ('pandas.core.window.rolling',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-1'),
  ('pandas.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io._util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-1'),
  ('pandas.io.clipboard',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.clipboards',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-1'),
  ('pandas.io.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-1'),
  ('pandas.io.excel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.excel._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odfreader',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odswriter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-1'),
  ('pandas.io.excel._openpyxl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-1'),
  ('pandas.io.excel._pyxlsb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-1'),
  ('pandas.io.excel._util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlrd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlsxwriter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-1'),
  ('pandas.io.feather_format',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-1'),
  ('pandas.io.formats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.formats._color_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-1'),
  ('pandas.io.formats.console',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-1'),
  ('pandas.io.formats.css',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-1'),
  ('pandas.io.formats.csvs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-1'),
  ('pandas.io.formats.excel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-1'),
  ('pandas.io.formats.format',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-1'),
  ('pandas.io.formats.html',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-1'),
  ('pandas.io.formats.info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-1'),
  ('pandas.io.formats.latex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE-1'),
  ('pandas.io.formats.printing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-1'),
  ('pandas.io.formats.string',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style_render',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-1'),
  ('pandas.io.formats.xml',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-1'),
  ('pandas.io.gbq',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-1'),
  ('pandas.io.html',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-1'),
  ('pandas.io.json',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.json._json',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-1'),
  ('pandas.io.json._normalize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-1'),
  ('pandas.io.json._table_schema',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-1'),
  ('pandas.io.orc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-1'),
  ('pandas.io.parquet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-1'),
  ('pandas.io.parsers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.base_parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.c_parser_wrapper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.python_parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.readers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-1'),
  ('pandas.io.pickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-1'),
  ('pandas.io.pytables',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-1'),
  ('pandas.io.sas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas7bdat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_xport',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sasreader',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-1'),
  ('pandas.io.spss',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-1'),
  ('pandas.io.sql',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-1'),
  ('pandas.io.stata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-1'),
  ('pandas.io.xml',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-1'),
  ('pandas.plotting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-1'),
  ('pandas.plotting._core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.boxplot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.converter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.groupby',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.hist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.style',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.timeseries',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE-1'),
  ('pandas.plotting._matplotlib.tools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE-1'),
  ('pandas.plotting._misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-1'),
  ('pandas.testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-1'),
  ('pandas.tseries',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-1'),
  ('pandas.tseries.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-1'),
  ('pandas.tseries.frequencies',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-1'),
  ('pandas.tseries.offsets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-1'),
  ('pandas.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.util._decorators',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-1'),
  ('pandas.util._exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-1'),
  ('pandas.util._print_versions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-1'),
  ('pandas.util._str_methods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE-1'),
  ('pandas.util._tester',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-1'),
  ('pandas.util._validators',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-1'),
  ('pandas.util.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-1'),
  ('pathlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pathlib.py',
   'PYMODULE-1'),
  ('pickle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pickle.py',
   'PYMODULE-1'),
  ('pkg_resources',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE-1'),
  ('pkgutil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pkgutil.py',
   'PYMODULE-1'),
  ('platform',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\platform.py',
   'PYMODULE-1'),
  ('platformdirs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE-1'),
  ('platformdirs.android',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE-1'),
  ('platformdirs.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE-1'),
  ('platformdirs.macos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE-1'),
  ('platformdirs.unix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE-1'),
  ('platformdirs.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE-1'),
  ('platformdirs.windows',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE-1'),
  ('plistlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\plistlib.py',
   'PYMODULE-1'),
  ('pooch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\__init__.py',
   'PYMODULE-1'),
  ('pooch._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\_version.py',
   'PYMODULE-1'),
  ('pooch.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\core.py',
   'PYMODULE-1'),
  ('pooch.downloaders',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\downloaders.py',
   'PYMODULE-1'),
  ('pooch.hashes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\hashes.py',
   'PYMODULE-1'),
  ('pooch.processors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\processors.py',
   'PYMODULE-1'),
  ('pooch.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pooch\\utils.py',
   'PYMODULE-1'),
  ('pprint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pprint.py',
   'PYMODULE-1'),
  ('profile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\profile.py',
   'PYMODULE-1'),
  ('pstats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pstats.py',
   'PYMODULE-1'),
  ('py_compile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\py_compile.py',
   'PYMODULE-1'),
  ('pycparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE-1'),
  ('pycparser.ast_transforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE-1'),
  ('pycparser.c_ast',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE-1'),
  ('pycparser.c_lexer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE-1'),
  ('pycparser.c_parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE-1'),
  ('pycparser.lextab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE-1'),
  ('pycparser.ply',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE-1'),
  ('pycparser.ply.lex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE-1'),
  ('pycparser.ply.yacc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE-1'),
  ('pycparser.plyparser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE-1'),
  ('pycparser.yacctab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE-1'),
  ('pydoc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\pydoc.py',
   'PYMODULE-1'),
  ('pyparsing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE-1'),
  ('pyparsing.actions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE-1'),
  ('pyparsing.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE-1'),
  ('pyparsing.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE-1'),
  ('pyparsing.diagram',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE-1'),
  ('pyparsing.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE-1'),
  ('pyparsing.helpers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE-1'),
  ('pyparsing.results',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE-1'),
  ('pyparsing.testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE-1'),
  ('pyparsing.unicode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE-1'),
  ('pyparsing.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE-1'),
  ('pyqtgraph',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.GraphicsScene',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'PYMODULE-1'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'PYMODULE-1'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'PYMODULE-1'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'PYMODULE-1'),
  ('pyqtgraph.Point',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt.QtCore',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt.QtGui',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt.QtWidgets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.Qt.internals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'PYMODULE-1'),
  ('pyqtgraph.SRTTransform',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'PYMODULE-1'),
  ('pyqtgraph.SRTTransform3D',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'PYMODULE-1'),
  ('pyqtgraph.SignalProxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'PYMODULE-1'),
  ('pyqtgraph.ThreadsafeTimer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'PYMODULE-1'),
  ('pyqtgraph.Transform3D',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'PYMODULE-1'),
  ('pyqtgraph.Vector',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'PYMODULE-1'),
  ('pyqtgraph.WidgetGroup',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'PYMODULE-1'),
  ('pyqtgraph.colormap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'PYMODULE-1'),
  ('pyqtgraph.colors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.colors.palette',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'PYMODULE-1'),
  ('pyqtgraph.console',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.console.CmdInput',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'PYMODULE-1'),
  ('pyqtgraph.console.Console',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'PYMODULE-1'),
  ('pyqtgraph.console.exception_widget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'PYMODULE-1'),
  ('pyqtgraph.console.repl_widget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'PYMODULE-1'),
  ('pyqtgraph.console.stackwidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.debug',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'PYMODULE-1'),
  ('pyqtgraph.exceptionHandling',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.CSVExporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.Exporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.HDF5Exporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.ImageExporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.Matplotlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.PrintExporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.exporters.SVGExporter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'PYMODULE-1'),
  ('pyqtgraph.functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'PYMODULE-1'),
  ('pyqtgraph.functions_numba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'PYMODULE-1'),
  ('pyqtgraph.functions_qimage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.AxisItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.GridItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ImageItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.LabelItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.LegendItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PlotItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ROI',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.TargetItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.TextItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ViewBox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'PYMODULE-1'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'PYMODULE-1'),
  ('pyqtgraph.icons',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.imageview',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.imageview.ImageView',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'PYMODULE-1'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'PYMODULE-1'),
  ('pyqtgraph.metaarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.metaarray.MetaArray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'PYMODULE-1'),
  ('pyqtgraph.multiprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.multiprocess.parallelizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'PYMODULE-1'),
  ('pyqtgraph.multiprocess.processes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'PYMODULE-1'),
  ('pyqtgraph.multiprocess.remoteproxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.Parameter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.ParameterItem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.ParameterSystem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.ParameterTree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.SystemSolver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.interactive',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'PYMODULE-1'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'PYMODULE-1'),
  ('pyqtgraph.reload',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'PYMODULE-1'),
  ('pyqtgraph.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.colorama',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.colorama.win32',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.colorama.winterm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.cprint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.cupy_helper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.mutex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'PYMODULE-1'),
  ('pyqtgraph.util.numba_helper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.BusyCursor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.CheckTable',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ColorButton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ColorMapButton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ColorMapMenu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ColorMapWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ComboBox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.DataFilterWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.DataTreeWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.FeedbackButton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.FileDialog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.GradientWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.GraphicsView',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.GroupBox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.JoystickButton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.LayoutWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.PathButton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.PlotWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ProgressDialog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.RawImageWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.SpinBox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.TableWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.TreeWidget',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.ValueLabel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'PYMODULE-1'),
  ('pyqtgraph.widgets.VerticalLabel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'PYMODULE-1'),
  ('pyriemann',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\__init__.py',
   'PYMODULE-1'),
  ('pyriemann._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\_version.py',
   'PYMODULE-1'),
  ('pyriemann.channelselection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\channelselection.py',
   'PYMODULE-1'),
  ('pyriemann.classification',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\classification.py',
   'PYMODULE-1'),
  ('pyriemann.clustering',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\clustering.py',
   'PYMODULE-1'),
  ('pyriemann.datasets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\datasets\\__init__.py',
   'PYMODULE-1'),
  ('pyriemann.datasets.sampling',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\datasets\\sampling.py',
   'PYMODULE-1'),
  ('pyriemann.datasets.simulated',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\datasets\\simulated.py',
   'PYMODULE-1'),
  ('pyriemann.embedding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\embedding.py',
   'PYMODULE-1'),
  ('pyriemann.estimation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\estimation.py',
   'PYMODULE-1'),
  ('pyriemann.preprocessing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\preprocessing.py',
   'PYMODULE-1'),
  ('pyriemann.spatialfilters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\spatialfilters.py',
   'PYMODULE-1'),
  ('pyriemann.stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\stats.py',
   'PYMODULE-1'),
  ('pyriemann.tangentspace',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\tangentspace.py',
   'PYMODULE-1'),
  ('pyriemann.transfer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\transfer\\__init__.py',
   'PYMODULE-1'),
  ('pyriemann.transfer._estimators',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\transfer\\_estimators.py',
   'PYMODULE-1'),
  ('pyriemann.transfer._rotate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\transfer\\_rotate.py',
   'PYMODULE-1'),
  ('pyriemann.transfer._tools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\transfer\\_tools.py',
   'PYMODULE-1'),
  ('pyriemann.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\__init__.py',
   'PYMODULE-1'),
  ('pyriemann.utils.ajd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\ajd.py',
   'PYMODULE-1'),
  ('pyriemann.utils.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\base.py',
   'PYMODULE-1'),
  ('pyriemann.utils.covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\covariance.py',
   'PYMODULE-1'),
  ('pyriemann.utils.distance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\distance.py',
   'PYMODULE-1'),
  ('pyriemann.utils.docs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\docs.py',
   'PYMODULE-1'),
  ('pyriemann.utils.geodesic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\geodesic.py',
   'PYMODULE-1'),
  ('pyriemann.utils.kernel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\kernel.py',
   'PYMODULE-1'),
  ('pyriemann.utils.mean',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\mean.py',
   'PYMODULE-1'),
  ('pyriemann.utils.median',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\median.py',
   'PYMODULE-1'),
  ('pyriemann.utils.tangentspace',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\tangentspace.py',
   'PYMODULE-1'),
  ('pyriemann.utils.test',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\test.py',
   'PYMODULE-1'),
  ('pyriemann.utils.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyriemann\\utils\\utils.py',
   'PYMODULE-1'),
  ('pythoncom',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE-1'),
  ('pyttsx3',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\__init__.py',
   'PYMODULE-1'),
  ('pyttsx3.driver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\driver.py',
   'PYMODULE-1'),
  ('pyttsx3.drivers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\drivers\\__init__.py',
   'PYMODULE-1'),
  ('pyttsx3.drivers.dummy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\drivers\\dummy.py',
   'PYMODULE-1'),
  ('pyttsx3.drivers.sapi5',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\drivers\\sapi5.py',
   'PYMODULE-1'),
  ('pyttsx3.engine',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\engine.py',
   'PYMODULE-1'),
  ('pyttsx3.voice',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyttsx3\\voice.py',
   'PYMODULE-1'),
  ('pytz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-1'),
  ('pytz.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-1'),
  ('pytz.lazy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-1'),
  ('pytz.tzfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-1'),
  ('pytz.tzinfo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-1'),
  ('pywin32_system32', '-', 'PYMODULE-1'),
  ('pywintypes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE-1'),
  ('queue',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\queue.py',
   'PYMODULE-1'),
  ('quopri',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\quopri.py',
   'PYMODULE-1'),
  ('random',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\random.py',
   'PYMODULE-1'),
  ('requests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-1'),
  ('requests.__version__',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-1'),
  ('requests._internal_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-1'),
  ('requests.adapters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-1'),
  ('requests.api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-1'),
  ('requests.auth',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-1'),
  ('requests.certs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-1'),
  ('requests.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-1'),
  ('requests.cookies',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-1'),
  ('requests.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-1'),
  ('requests.hooks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-1'),
  ('requests.models',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-1'),
  ('requests.packages',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-1'),
  ('requests.sessions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-1'),
  ('requests.status_codes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-1'),
  ('requests.structures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-1'),
  ('requests.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-1'),
  ('rlcompleter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\rlcompleter.py',
   'PYMODULE-1'),
  ('runpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\runpy.py',
   'PYMODULE-1'),
  ('scipy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE-1'),
  ('scipy.__config__',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE-1'),
  ('scipy._distributor_init',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE-1'),
  ('scipy._lib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE-1'),
  ('scipy._lib._bunch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE-1'),
  ('scipy._lib._ccallback',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE-1'),
  ('scipy._lib._disjoint_set',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_disjoint_set.py',
   'PYMODULE-1'),
  ('scipy._lib._docscrape',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE-1'),
  ('scipy._lib._finite_differences',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE-1'),
  ('scipy._lib._pep440',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE-1'),
  ('scipy._lib._testutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE-1'),
  ('scipy._lib._threadsafety',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE-1'),
  ('scipy._lib._uarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE-1'),
  ('scipy._lib._uarray._backend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE-1'),
  ('scipy._lib._util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE-1'),
  ('scipy._lib.decorator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE-1'),
  ('scipy._lib.deprecation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE-1'),
  ('scipy._lib.doccer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE-1'),
  ('scipy._lib.uarray',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE-1'),
  ('scipy.cluster',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\__init__.py',
   'PYMODULE-1'),
  ('scipy.cluster.hierarchy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\hierarchy.py',
   'PYMODULE-1'),
  ('scipy.cluster.vq',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\vq.py',
   'PYMODULE-1'),
  ('scipy.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE-1'),
  ('scipy.constants._codata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE-1'),
  ('scipy.constants._constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE-1'),
  ('scipy.constants.codata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE-1'),
  ('scipy.constants.constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE-1'),
  ('scipy.fft',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE-1'),
  ('scipy.fft._backend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE-1'),
  ('scipy.fft._basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE-1'),
  ('scipy.fft._fftlog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE-1'),
  ('scipy.fft._fftlog_multimethods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_fftlog_multimethods.py',
   'PYMODULE-1'),
  ('scipy.fft._helper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE-1'),
  ('scipy.fft._pocketfft',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE-1'),
  ('scipy.fft._pocketfft.basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE-1'),
  ('scipy.fft._pocketfft.helper',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE-1'),
  ('scipy.fft._pocketfft.realtransforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE-1'),
  ('scipy.fft._realtransforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE-1'),
  ('scipy.integrate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE-1'),
  ('scipy.integrate._bvp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.bdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.ivp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.lsoda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.radau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE-1'),
  ('scipy.integrate._ivp.rk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE-1'),
  ('scipy.integrate._ode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE-1'),
  ('scipy.integrate._odepack_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE-1'),
  ('scipy.integrate._quad_vec',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE-1'),
  ('scipy.integrate._quadpack_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE-1'),
  ('scipy.integrate._quadrature',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE-1'),
  ('scipy.integrate.dop',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE-1'),
  ('scipy.integrate.lsoda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE-1'),
  ('scipy.integrate.odepack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE-1'),
  ('scipy.integrate.quadpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE-1'),
  ('scipy.integrate.vode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE-1'),
  ('scipy.interpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE-1'),
  ('scipy.interpolate._bsplines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE-1'),
  ('scipy.interpolate._cubic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE-1'),
  ('scipy.interpolate._fitpack2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE-1'),
  ('scipy.interpolate._fitpack_impl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE-1'),
  ('scipy.interpolate._fitpack_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE-1'),
  ('scipy.interpolate._interpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE-1'),
  ('scipy.interpolate._ndgriddata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE-1'),
  ('scipy.interpolate._pade',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE-1'),
  ('scipy.interpolate._polyint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE-1'),
  ('scipy.interpolate._rbf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE-1'),
  ('scipy.interpolate._rbfinterp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE-1'),
  ('scipy.interpolate._rgi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE-1'),
  ('scipy.interpolate.fitpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE-1'),
  ('scipy.interpolate.fitpack2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE-1'),
  ('scipy.interpolate.interpolate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE-1'),
  ('scipy.interpolate.ndgriddata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE-1'),
  ('scipy.interpolate.polyint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE-1'),
  ('scipy.interpolate.rbf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE-1'),
  ('scipy.io',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\__init__.py',
   'PYMODULE-1'),
  ('scipy.io._fortran',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_fortran.py',
   'PYMODULE-1'),
  ('scipy.io._harwell_boeing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\__init__.py',
   'PYMODULE-1'),
  ('scipy.io._harwell_boeing._fortran_format_parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\_fortran_format_parser.py',
   'PYMODULE-1'),
  ('scipy.io._harwell_boeing.hb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\hb.py',
   'PYMODULE-1'),
  ('scipy.io._idl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_idl.py',
   'PYMODULE-1'),
  ('scipy.io._mmio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_mmio.py',
   'PYMODULE-1'),
  ('scipy.io._netcdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\_netcdf.py',
   'PYMODULE-1'),
  ('scipy.io.arff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\arff\\__init__.py',
   'PYMODULE-1'),
  ('scipy.io.arff._arffread',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\arff\\_arffread.py',
   'PYMODULE-1'),
  ('scipy.io.arff.arffread',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\arff\\arffread.py',
   'PYMODULE-1'),
  ('scipy.io.harwell_boeing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\harwell_boeing.py',
   'PYMODULE-1'),
  ('scipy.io.idl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\idl.py',
   'PYMODULE-1'),
  ('scipy.io.matlab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\__init__.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._byteordercodes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_byteordercodes.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._mio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._mio4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio4.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._mio5',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio5.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._mio5_params',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio5_params.py',
   'PYMODULE-1'),
  ('scipy.io.matlab._miobase',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_miobase.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.byteordercodes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\byteordercodes.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio4.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio5',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio5.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio5_params',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio5_params.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio5_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio5_utils.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.mio_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\mio_utils.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.miobase',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\miobase.py',
   'PYMODULE-1'),
  ('scipy.io.matlab.streams',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\streams.py',
   'PYMODULE-1'),
  ('scipy.io.mmio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\mmio.py',
   'PYMODULE-1'),
  ('scipy.io.netcdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\netcdf.py',
   'PYMODULE-1'),
  ('scipy.io.wavfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\wavfile.py',
   'PYMODULE-1'),
  ('scipy.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE-1'),
  ('scipy.linalg._basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_cholesky',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_cossin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_ldl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_lu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_polar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_qr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_qz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_schur',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE-1'),
  ('scipy.linalg._decomp_svd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE-1'),
  ('scipy.linalg._expm_frechet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE-1'),
  ('scipy.linalg._flinalg_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_flinalg_py.py',
   'PYMODULE-1'),
  ('scipy.linalg._interpolative_backend',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_interpolative_backend.py',
   'PYMODULE-1'),
  ('scipy.linalg._matfuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE-1'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE-1'),
  ('scipy.linalg._matfuncs_sqrtm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE-1'),
  ('scipy.linalg._misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE-1'),
  ('scipy.linalg._procrustes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE-1'),
  ('scipy.linalg._sketches',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE-1'),
  ('scipy.linalg._solvers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE-1'),
  ('scipy.linalg._special_matrices',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE-1'),
  ('scipy.linalg.basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE-1'),
  ('scipy.linalg.blas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp_cholesky',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp_lu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp_qr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp_schur',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE-1'),
  ('scipy.linalg.decomp_svd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE-1'),
  ('scipy.linalg.flinalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\flinalg.py',
   'PYMODULE-1'),
  ('scipy.linalg.interpolative',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE-1'),
  ('scipy.linalg.lapack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE-1'),
  ('scipy.linalg.matfuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE-1'),
  ('scipy.linalg.misc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE-1'),
  ('scipy.linalg.special_matrices',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE-1'),
  ('scipy.ndimage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE-1'),
  ('scipy.ndimage._filters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE-1'),
  ('scipy.ndimage._fourier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE-1'),
  ('scipy.ndimage._interpolation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE-1'),
  ('scipy.ndimage._measurements',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE-1'),
  ('scipy.ndimage._morphology',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE-1'),
  ('scipy.ndimage._ni_docstrings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE-1'),
  ('scipy.ndimage._ni_support',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE-1'),
  ('scipy.ndimage.filters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE-1'),
  ('scipy.ndimage.fourier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE-1'),
  ('scipy.ndimage.interpolation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE-1'),
  ('scipy.ndimage.measurements',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE-1'),
  ('scipy.ndimage.morphology',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE-1'),
  ('scipy.optimize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._basinhopping',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE-1'),
  ('scipy.optimize._cobyla_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE-1'),
  ('scipy.optimize._constraints',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE-1'),
  ('scipy.optimize._differentiable_functions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE-1'),
  ('scipy.optimize._differentialevolution',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE-1'),
  ('scipy.optimize._direct_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE-1'),
  ('scipy.optimize._dual_annealing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE-1'),
  ('scipy.optimize._hessian_update_strategy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE-1'),
  ('scipy.optimize._highs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_highs\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._lbfgsb_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE-1'),
  ('scipy.optimize._linesearch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_doc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_highs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_ip',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_rs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_simplex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE-1'),
  ('scipy.optimize._linprog_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.bvls',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.dogbox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.least_squares',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.lsq_linear',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.trf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE-1'),
  ('scipy.optimize._lsq.trf_linear',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE-1'),
  ('scipy.optimize._milp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE-1'),
  ('scipy.optimize._minimize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE-1'),
  ('scipy.optimize._minpack_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE-1'),
  ('scipy.optimize._nnls',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE-1'),
  ('scipy.optimize._nonlin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE-1'),
  ('scipy.optimize._numdiff',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE-1'),
  ('scipy.optimize._optimize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE-1'),
  ('scipy.optimize._qap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE-1'),
  ('scipy.optimize._remove_redundancy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE-1'),
  ('scipy.optimize._root',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE-1'),
  ('scipy.optimize._root_scalar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE-1'),
  ('scipy.optimize._shgo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE-1'),
  ('scipy.optimize._shgo_lib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._shgo_lib._complex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE-1'),
  ('scipy.optimize._shgo_lib._vertex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE-1'),
  ('scipy.optimize._slsqp_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE-1'),
  ('scipy.optimize._spectral',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE-1'),
  ('scipy.optimize._tnc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE-1'),
  ('scipy.optimize._trlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.projections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.report',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_dogleg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_exact',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_krylov',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE-1'),
  ('scipy.optimize._trustregion_ncg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE-1'),
  ('scipy.optimize._zeros_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE-1'),
  ('scipy.optimize.cobyla',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE-1'),
  ('scipy.optimize.lbfgsb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE-1'),
  ('scipy.optimize.linesearch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE-1'),
  ('scipy.optimize.minpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE-1'),
  ('scipy.optimize.minpack2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE-1'),
  ('scipy.optimize.moduleTNC',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE-1'),
  ('scipy.optimize.nonlin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE-1'),
  ('scipy.optimize.optimize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE-1'),
  ('scipy.optimize.slsqp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE-1'),
  ('scipy.optimize.tnc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE-1'),
  ('scipy.optimize.zeros',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE-1'),
  ('scipy.signal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\__init__.py',
   'PYMODULE-1'),
  ('scipy.signal._arraytools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_arraytools.py',
   'PYMODULE-1'),
  ('scipy.signal._bsplines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_bsplines.py',
   'PYMODULE-1'),
  ('scipy.signal._czt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_czt.py',
   'PYMODULE-1'),
  ('scipy.signal._filter_design',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_filter_design.py',
   'PYMODULE-1'),
  ('scipy.signal._fir_filter_design',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_fir_filter_design.py',
   'PYMODULE-1'),
  ('scipy.signal._lti_conversion',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_lti_conversion.py',
   'PYMODULE-1'),
  ('scipy.signal._ltisys',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_ltisys.py',
   'PYMODULE-1'),
  ('scipy.signal._max_len_seq',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_max_len_seq.py',
   'PYMODULE-1'),
  ('scipy.signal._peak_finding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_peak_finding.py',
   'PYMODULE-1'),
  ('scipy.signal._savitzky_golay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_savitzky_golay.py',
   'PYMODULE-1'),
  ('scipy.signal._signaltools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_signaltools.py',
   'PYMODULE-1'),
  ('scipy.signal._spectral_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_spectral_py.py',
   'PYMODULE-1'),
  ('scipy.signal._upfirdn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_upfirdn.py',
   'PYMODULE-1'),
  ('scipy.signal._waveforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_waveforms.py',
   'PYMODULE-1'),
  ('scipy.signal._wavelets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_wavelets.py',
   'PYMODULE-1'),
  ('scipy.signal.bsplines',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\bsplines.py',
   'PYMODULE-1'),
  ('scipy.signal.filter_design',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\filter_design.py',
   'PYMODULE-1'),
  ('scipy.signal.fir_filter_design',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\fir_filter_design.py',
   'PYMODULE-1'),
  ('scipy.signal.lti_conversion',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\lti_conversion.py',
   'PYMODULE-1'),
  ('scipy.signal.ltisys',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\ltisys.py',
   'PYMODULE-1'),
  ('scipy.signal.signaltools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\signaltools.py',
   'PYMODULE-1'),
  ('scipy.signal.spectral',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\spectral.py',
   'PYMODULE-1'),
  ('scipy.signal.spline',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\spline.py',
   'PYMODULE-1'),
  ('scipy.signal.waveforms',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\waveforms.py',
   'PYMODULE-1'),
  ('scipy.signal.wavelets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\wavelets.py',
   'PYMODULE-1'),
  ('scipy.signal.windows',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\windows\\__init__.py',
   'PYMODULE-1'),
  ('scipy.signal.windows._windows',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\windows\\_windows.py',
   'PYMODULE-1'),
  ('scipy.signal.windows.windows',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\windows\\windows.py',
   'PYMODULE-1'),
  ('scipy.sparse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE-1'),
  ('scipy.sparse._bsr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE-1'),
  ('scipy.sparse._compressed',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE-1'),
  ('scipy.sparse._construct',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE-1'),
  ('scipy.sparse._coo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE-1'),
  ('scipy.sparse._csc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE-1'),
  ('scipy.sparse._csr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE-1'),
  ('scipy.sparse._data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE-1'),
  ('scipy.sparse._dia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE-1'),
  ('scipy.sparse._dok',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE-1'),
  ('scipy.sparse._extract',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE-1'),
  ('scipy.sparse._index',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE-1'),
  ('scipy.sparse._lil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE-1'),
  ('scipy.sparse._matrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE-1'),
  ('scipy.sparse._matrix_io',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE-1'),
  ('scipy.sparse._spfuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE-1'),
  ('scipy.sparse._sputils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE-1'),
  ('scipy.sparse.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE-1'),
  ('scipy.sparse.bsr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE-1'),
  ('scipy.sparse.compressed',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE-1'),
  ('scipy.sparse.construct',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE-1'),
  ('scipy.sparse.coo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE-1'),
  ('scipy.sparse.csc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE-1'),
  ('scipy.sparse.csgraph',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.csgraph._laplacian',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE-1'),
  ('scipy.sparse.csgraph._validation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE-1'),
  ('scipy.sparse.csr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE-1'),
  ('scipy.sparse.data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE-1'),
  ('scipy.sparse.dia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE-1'),
  ('scipy.sparse.dok',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE-1'),
  ('scipy.sparse.extract',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE-1'),
  ('scipy.sparse.lil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._dsolve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen._svds',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen.arpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._expm_multiply',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._interface',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.iterative',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.minres',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._isolve.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._matfuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._norm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._onenormest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE-1'),
  ('scipy.sparse.linalg._svdp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg.dsolve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg.eigen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg.interface',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg.isolve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE-1'),
  ('scipy.sparse.linalg.matfuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE-1'),
  ('scipy.sparse.sparsetools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE-1'),
  ('scipy.sparse.sputils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE-1'),
  ('scipy.spatial',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE-1'),
  ('scipy.spatial._geometric_slerp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE-1'),
  ('scipy.spatial._kdtree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE-1'),
  ('scipy.spatial._plotutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE-1'),
  ('scipy.spatial._procrustes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE-1'),
  ('scipy.spatial._spherical_voronoi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE-1'),
  ('scipy.spatial.ckdtree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE-1'),
  ('scipy.spatial.distance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE-1'),
  ('scipy.spatial.kdtree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE-1'),
  ('scipy.spatial.qhull',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE-1'),
  ('scipy.spatial.transform',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE-1'),
  ('scipy.spatial.transform._rotation_groups',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE-1'),
  ('scipy.spatial.transform._rotation_spline',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE-1'),
  ('scipy.spatial.transform.rotation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE-1'),
  ('scipy.special',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE-1'),
  ('scipy.special._add_newdocs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_add_newdocs.py',
   'PYMODULE-1'),
  ('scipy.special._basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE-1'),
  ('scipy.special._ellip_harm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE-1'),
  ('scipy.special._lambertw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE-1'),
  ('scipy.special._logsumexp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE-1'),
  ('scipy.special._orthogonal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE-1'),
  ('scipy.special._sf_error',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE-1'),
  ('scipy.special._spfun_stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE-1'),
  ('scipy.special._spherical_bessel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE-1'),
  ('scipy.special.add_newdocs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE-1'),
  ('scipy.special.basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE-1'),
  ('scipy.special.orthogonal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE-1'),
  ('scipy.special.sf_error',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE-1'),
  ('scipy.special.specfun',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE-1'),
  ('scipy.special.spfun_stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE-1'),
  ('scipy.stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE-1'),
  ('scipy.stats._axis_nan_policy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE-1'),
  ('scipy.stats._binned_statistic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE-1'),
  ('scipy.stats._binomtest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE-1'),
  ('scipy.stats._boost',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\__init__.py',
   'PYMODULE-1'),
  ('scipy.stats._censored_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE-1'),
  ('scipy.stats._common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE-1'),
  ('scipy.stats._constants',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE-1'),
  ('scipy.stats._continuous_distns',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE-1'),
  ('scipy.stats._covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE-1'),
  ('scipy.stats._crosstab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE-1'),
  ('scipy.stats._discrete_distns',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE-1'),
  ('scipy.stats._distn_infrastructure',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE-1'),
  ('scipy.stats._distr_params',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE-1'),
  ('scipy.stats._entropy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE-1'),
  ('scipy.stats._fit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE-1'),
  ('scipy.stats._hypotests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE-1'),
  ('scipy.stats._kde',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE-1'),
  ('scipy.stats._ksstats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE-1'),
  ('scipy.stats._levy_stable',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE-1'),
  ('scipy.stats._mannwhitneyu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE-1'),
  ('scipy.stats._morestats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE-1'),
  ('scipy.stats._mstats_basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE-1'),
  ('scipy.stats._mstats_extras',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE-1'),
  ('scipy.stats._multicomp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE-1'),
  ('scipy.stats._multivariate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE-1'),
  ('scipy.stats._odds_ratio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE-1'),
  ('scipy.stats._page_trend_test',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE-1'),
  ('scipy.stats._qmc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE-1'),
  ('scipy.stats._qmvnt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE-1'),
  ('scipy.stats._rcont',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE-1'),
  ('scipy.stats._relative_risk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE-1'),
  ('scipy.stats._resampling',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE-1'),
  ('scipy.stats._rvs_sampling',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_rvs_sampling.py',
   'PYMODULE-1'),
  ('scipy.stats._sensitivity_analysis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE-1'),
  ('scipy.stats._stats_mstats_common',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE-1'),
  ('scipy.stats._stats_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE-1'),
  ('scipy.stats._survival',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE-1'),
  ('scipy.stats._tukeylambda_stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE-1'),
  ('scipy.stats._variation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE-1'),
  ('scipy.stats._warnings_errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE-1'),
  ('scipy.stats.biasedurn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE-1'),
  ('scipy.stats.contingency',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE-1'),
  ('scipy.stats.distributions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE-1'),
  ('scipy.stats.kde',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE-1'),
  ('scipy.stats.morestats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE-1'),
  ('scipy.stats.mstats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE-1'),
  ('scipy.stats.mstats_basic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE-1'),
  ('scipy.stats.mstats_extras',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE-1'),
  ('scipy.stats.mvn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE-1'),
  ('scipy.stats.qmc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE-1'),
  ('scipy.stats.statlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\statlib.py',
   'PYMODULE-1'),
  ('scipy.stats.stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE-1'),
  ('scipy.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\version.py',
   'PYMODULE-1'),
  ('secrets',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\secrets.py',
   'PYMODULE-1'),
  ('selectors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\selectors.py',
   'PYMODULE-1'),
  ('services', 'D:\\NK_Python\\脑机接口康复训练\\services\\__init__.py', 'PYMODULE-1'),
  ('services.api_client',
   'D:\\NK_Python\\脑机接口康复训练\\services\\api_client.py',
   'PYMODULE-1'),
  ('services.auth_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\auth_service.py',
   'PYMODULE-1'),
  ('services.bluetooth',
   'D:\\NK_Python\\脑机接口康复训练\\services\\bluetooth\\__init__.py',
   'PYMODULE-1'),
  ('services.bluetooth.standard_bleak_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\bluetooth\\standard_bleak_manager.py',
   'PYMODULE-1'),
  ('services.classifier_training_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\classifier_training_manager.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\__init__.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.kalman_processor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\kalman_processor.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.preprocessing_config',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\preprocessing_config.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.preprocessing_pipeline',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\preprocessing_pipeline.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.realtime_integration_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\realtime_integration_manager.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.rls_filter',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\rls_filter.py',
   'PYMODULE-1'),
  ('services.eeg_preprocessing.traditional_filter',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_preprocessing\\traditional_filter.py',
   'PYMODULE-1'),
  ('services.eeg_processing',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_processing\\__init__.py',
   'PYMODULE-1'),
  ('services.eeg_processing.eeg_data_processor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\eeg_processing\\eeg_data_processor.py',
   'PYMODULE-1'),
  ('services.feature_extraction',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\__init__.py',
   'PYMODULE-1'),
  ('services.feature_extraction.base_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\base_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.config',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\config.py',
   'PYMODULE-1'),
  ('services.feature_extraction.fbcsp_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\fbcsp_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.individual_feature_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\individual_feature_manager.py',
   'PYMODULE-1'),
  ('services.feature_extraction.plv_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\plv_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.plv_utils',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\plv_utils.py',
   'PYMODULE-1'),
  ('services.feature_extraction.riemannian_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\riemannian_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.tangent_space_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\tangent_space_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.tef_extractor',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\tef_extractor.py',
   'PYMODULE-1'),
  ('services.feature_extraction.utils',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\utils\\__init__.py',
   'PYMODULE-1'),
  ('services.feature_extraction.utils.signal_utils',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\utils\\signal_utils.py',
   'PYMODULE-1'),
  ('services.feature_extraction.utils.validation_utils',
   'D:\\NK_Python\\脑机接口康复训练\\services\\feature_extraction\\utils\\validation_utils.py',
   'PYMODULE-1'),
  ('services.license_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\license_manager.py',
   'PYMODULE-1'),
  ('services.multi_round_training_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\multi_round_training_manager.py',
   'PYMODULE-1'),
  ('services.patient_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\patient_service.py',
   'PYMODULE-1'),
  ('services.reference_data_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\reference_data_service.py',
   'PYMODULE-1'),
  ('services.report_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\report_service.py',
   'PYMODULE-1'),
  ('services.stimulation',
   'D:\\NK_Python\\脑机接口康复训练\\services\\stimulation\\__init__.py',
   'PYMODULE-1'),
  ('services.stimulation.stimulation_device',
   'D:\\NK_Python\\脑机接口康复训练\\services\\stimulation\\stimulation_device.py',
   'PYMODULE-1'),
  ('services.temporal_smoothing',
   'D:\\NK_Python\\脑机接口康复训练\\services\\temporal_smoothing.py',
   'PYMODULE-1'),
  ('services.training_session_manager',
   'D:\\NK_Python\\脑机接口康复训练\\services\\training_session_manager.py',
   'PYMODULE-1'),
  ('services.treatment_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\treatment_service.py',
   'PYMODULE-1'),
  ('services.user_service',
   'D:\\NK_Python\\脑机接口康复训练\\services\\user_service.py',
   'PYMODULE-1'),
  ('services.weighted_voting_classifier',
   'D:\\NK_Python\\脑机接口康复训练\\services\\weighted_voting_classifier.py',
   'PYMODULE-1'),
  ('setuptools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._core_metadata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE-1'),
  ('setuptools._distutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils._log',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE-1'),
  ('setuptools._distutils._macos_compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE-1'),
  ('setuptools._distutils._modified',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE-1'),
  ('setuptools._distutils._msvccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.archive_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.ccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.cmd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command._framework_compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.bdist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.bdist_dumb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.bdist_rpm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build_clib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build_ext',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build_py',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build_scripts',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.check',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.clean',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install_data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install_egg_info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install_headers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install_lib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.install_scripts',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.sdist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat.numpy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat.py39',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE-1'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.cygwin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.msvc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.unix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.zos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE-1'),
  ('setuptools._distutils.core',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE-1'),
  ('setuptools._distutils.cygwinccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.debug',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE-1'),
  ('setuptools._distutils.dep_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.dir_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.dist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE-1'),
  ('setuptools._distutils.extension',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE-1'),
  ('setuptools._distutils.fancy_getopt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE-1'),
  ('setuptools._distutils.file_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.filelist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.log',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE-1'),
  ('setuptools._distutils.spawn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE-1'),
  ('setuptools._distutils.sysconfig',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE-1'),
  ('setuptools._distutils.text_file',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE-1'),
  ('setuptools._distutils.unixccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE-1'),
  ('setuptools._distutils.versionpredicate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE-1'),
  ('setuptools._distutils.zosccompiler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE-1'),
  ('setuptools._entry_points',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE-1'),
  ('setuptools._imp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE-1'),
  ('setuptools._importlib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE-1'),
  ('setuptools._itertools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE-1'),
  ('setuptools._normalization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE-1'),
  ('setuptools._path',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE-1'),
  ('setuptools._reqs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE-1'),
  ('setuptools._shutil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE-1'),
  ('setuptools._static',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE-1'),
  ('setuptools._vendor', '-', 'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._functools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._meta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._elffile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._manylinux',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._musllinux',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._structures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._tokenizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.markers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.requirements',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.specifiers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.tags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._re',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.compat.py310',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.glob',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE-1'),
  ('setuptools.archive_util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE-1'),
  ('setuptools.command',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.command._requirestxt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE-1'),
  ('setuptools.command.bdist_egg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE-1'),
  ('setuptools.command.bdist_wheel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE-1'),
  ('setuptools.command.build',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE-1'),
  ('setuptools.command.egg_info',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE-1'),
  ('setuptools.command.sdist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE-1'),
  ('setuptools.command.setopt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE-1'),
  ('setuptools.compat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.compat.py310',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE-1'),
  ('setuptools.compat.py311',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE-1'),
  ('setuptools.compat.py39',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools.config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.config._apply_pyprojecttoml',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.formats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE-1'),
  ('setuptools.config.expand',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE-1'),
  ('setuptools.config.pyprojecttoml',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE-1'),
  ('setuptools.config.setupcfg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE-1'),
  ('setuptools.depends',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE-1'),
  ('setuptools.discovery',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE-1'),
  ('setuptools.dist',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE-1'),
  ('setuptools.errors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE-1'),
  ('setuptools.extension',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE-1'),
  ('setuptools.glob',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE-1'),
  ('setuptools.installer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE-1'),
  ('setuptools.logging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE-1'),
  ('setuptools.monkey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE-1'),
  ('setuptools.msvc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE-1'),
  ('setuptools.unicode_utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE-1'),
  ('setuptools.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE-1'),
  ('setuptools.warnings',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE-1'),
  ('setuptools.wheel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE-1'),
  ('setuptools.windows_support',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE-1'),
  ('shiboken6',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE-1'),
  ('shiboken6._config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\_config.py',
   'PYMODULE-1'),
  ('shiboken6._git_shiboken_module_version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\_git_shiboken_module_version.py',
   'PYMODULE-1'),
  ('shlex',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\shlex.py',
   'PYMODULE-1'),
  ('shutil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\shutil.py',
   'PYMODULE-1'),
  ('signal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\signal.py',
   'PYMODULE-1'),
  ('site',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site.py',
   'PYMODULE-1'),
  ('six',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\six.py',
   'PYMODULE-1'),
  ('sklearn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.__check_build',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\__check_build\\__init__.py',
   'PYMODULE-1'),
  ('sklearn._config',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_config.py',
   'PYMODULE-1'),
  ('sklearn._distributor_init',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_distributor_init.py',
   'PYMODULE-1'),
  ('sklearn._loss',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_loss\\__init__.py',
   'PYMODULE-1'),
  ('sklearn._loss.link',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_loss\\link.py',
   'PYMODULE-1'),
  ('sklearn._loss.loss',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_loss\\loss.py',
   'PYMODULE-1'),
  ('sklearn.base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\base.py',
   'PYMODULE-1'),
  ('sklearn.cluster',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.cluster._affinity_propagation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_affinity_propagation.py',
   'PYMODULE-1'),
  ('sklearn.cluster._agglomerative',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_agglomerative.py',
   'PYMODULE-1'),
  ('sklearn.cluster._bicluster',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_bicluster.py',
   'PYMODULE-1'),
  ('sklearn.cluster._birch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_birch.py',
   'PYMODULE-1'),
  ('sklearn.cluster._bisect_k_means',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_bisect_k_means.py',
   'PYMODULE-1'),
  ('sklearn.cluster._dbscan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_dbscan.py',
   'PYMODULE-1'),
  ('sklearn.cluster._feature_agglomeration',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_feature_agglomeration.py',
   'PYMODULE-1'),
  ('sklearn.cluster._hdbscan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.cluster._hdbscan.hdbscan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\hdbscan.py',
   'PYMODULE-1'),
  ('sklearn.cluster._kmeans',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py',
   'PYMODULE-1'),
  ('sklearn.cluster._mean_shift',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_mean_shift.py',
   'PYMODULE-1'),
  ('sklearn.cluster._optics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_optics.py',
   'PYMODULE-1'),
  ('sklearn.cluster._spectral',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_spectral.py',
   'PYMODULE-1'),
  ('sklearn.covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.covariance._elliptic_envelope',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\_elliptic_envelope.py',
   'PYMODULE-1'),
  ('sklearn.covariance._empirical_covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\_empirical_covariance.py',
   'PYMODULE-1'),
  ('sklearn.covariance._graph_lasso',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\_graph_lasso.py',
   'PYMODULE-1'),
  ('sklearn.covariance._robust_covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\_robust_covariance.py',
   'PYMODULE-1'),
  ('sklearn.covariance._shrunk_covariance',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\covariance\\_shrunk_covariance.py',
   'PYMODULE-1'),
  ('sklearn.decomposition',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_base.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._dict_learning',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_dict_learning.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._factor_analysis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_factor_analysis.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._fastica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_fastica.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._incremental_pca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_incremental_pca.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._kernel_pca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_kernel_pca.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._lda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_lda.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._nmf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_nmf.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._pca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_pca.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._sparse_pca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_sparse_pca.py',
   'PYMODULE-1'),
  ('sklearn.decomposition._truncated_svd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_truncated_svd.py',
   'PYMODULE-1'),
  ('sklearn.discriminant_analysis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\discriminant_analysis.py',
   'PYMODULE-1'),
  ('sklearn.dummy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\dummy.py',
   'PYMODULE-1'),
  ('sklearn.ensemble',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._bagging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_bagging.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_base.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._forest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_forest.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._gb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_gb.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._gb_losses',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_gb_losses.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._hist_gradient_boosting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._hist_gradient_boosting.binning',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\binning.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._hist_gradient_boosting.gradient_boosting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\gradient_boosting.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._hist_gradient_boosting.grower',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\grower.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._hist_gradient_boosting.predictor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\predictor.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._iforest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_iforest.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._stacking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_stacking.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._voting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_voting.py',
   'PYMODULE-1'),
  ('sklearn.ensemble._weight_boosting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_weight_boosting.py',
   'PYMODULE-1'),
  ('sklearn.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\exceptions.py',
   'PYMODULE-1'),
  ('sklearn.externals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\externals\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.externals._packaging',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\externals\\_packaging\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.externals._packaging._structures',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\externals\\_packaging\\_structures.py',
   'PYMODULE-1'),
  ('sklearn.externals._packaging.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\externals\\_packaging\\version.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction._dict_vectorizer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\_dict_vectorizer.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction._hash',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\_hash.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction._stop_words',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\_stop_words.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction.image',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\image.py',
   'PYMODULE-1'),
  ('sklearn.feature_extraction.text',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\text.py',
   'PYMODULE-1'),
  ('sklearn.gaussian_process',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\gaussian_process\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.gaussian_process._gpc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpc.py',
   'PYMODULE-1'),
  ('sklearn.gaussian_process._gpr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py',
   'PYMODULE-1'),
  ('sklearn.gaussian_process.kernels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\gaussian_process\\kernels.py',
   'PYMODULE-1'),
  ('sklearn.isotonic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\isotonic.py',
   'PYMODULE-1'),
  ('sklearn.linear_model',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_base.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._bayes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_bayes.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._coordinate_descent',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_coordinate_descent.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._glm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._glm._newton_solver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\_newton_solver.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._glm.glm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\glm.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._huber',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_huber.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._least_angle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_least_angle.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._linear_loss',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_linear_loss.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._logistic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._omp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_omp.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._passive_aggressive',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_passive_aggressive.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._perceptron',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_perceptron.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._quantile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_quantile.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._ransac',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_ransac.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._ridge',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_ridge.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._sag',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_sag.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._stochastic_gradient',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_stochastic_gradient.py',
   'PYMODULE-1'),
  ('sklearn.linear_model._theil_sen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_theil_sen.py',
   'PYMODULE-1'),
  ('sklearn.manifold',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.manifold._isomap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_isomap.py',
   'PYMODULE-1'),
  ('sklearn.manifold._locally_linear',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_locally_linear.py',
   'PYMODULE-1'),
  ('sklearn.manifold._mds',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_mds.py',
   'PYMODULE-1'),
  ('sklearn.manifold._spectral_embedding',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_spectral_embedding.py',
   'PYMODULE-1'),
  ('sklearn.manifold._t_sne',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_t_sne.py',
   'PYMODULE-1'),
  ('sklearn.metrics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.metrics._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_base.py',
   'PYMODULE-1'),
  ('sklearn.metrics._classification',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_classification.py',
   'PYMODULE-1'),
  ('sklearn.metrics._pairwise_distances_reduction',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.metrics._pairwise_distances_reduction._dispatcher',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_dispatcher.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot.confusion_matrix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\confusion_matrix.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot.det_curve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\det_curve.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot.precision_recall_curve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\precision_recall_curve.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot.regression',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\regression.py',
   'PYMODULE-1'),
  ('sklearn.metrics._plot.roc_curve',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_plot\\roc_curve.py',
   'PYMODULE-1'),
  ('sklearn.metrics._ranking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_ranking.py',
   'PYMODULE-1'),
  ('sklearn.metrics._regression',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_regression.py',
   'PYMODULE-1'),
  ('sklearn.metrics._scorer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_scorer.py',
   'PYMODULE-1'),
  ('sklearn.metrics.cluster',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\cluster\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.metrics.cluster._bicluster',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_bicluster.py',
   'PYMODULE-1'),
  ('sklearn.metrics.cluster._supervised',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_supervised.py',
   'PYMODULE-1'),
  ('sklearn.metrics.cluster._unsupervised',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_unsupervised.py',
   'PYMODULE-1'),
  ('sklearn.metrics.pairwise',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\pairwise.py',
   'PYMODULE-1'),
  ('sklearn.model_selection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.model_selection._plot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\_plot.py',
   'PYMODULE-1'),
  ('sklearn.model_selection._search',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\_search.py',
   'PYMODULE-1'),
  ('sklearn.model_selection._search_successive_halving',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\_search_successive_halving.py',
   'PYMODULE-1'),
  ('sklearn.model_selection._split',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\_split.py',
   'PYMODULE-1'),
  ('sklearn.model_selection._validation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\model_selection\\_validation.py',
   'PYMODULE-1'),
  ('sklearn.multiclass',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\multiclass.py',
   'PYMODULE-1'),
  ('sklearn.neighbors',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_base.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._classification',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_classification.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._graph',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_graph.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._kde',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_kde.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._lof',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_lof.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._nca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_nca.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._nearest_centroid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_nearest_centroid.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._regression',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_regression.py',
   'PYMODULE-1'),
  ('sklearn.neighbors._unsupervised',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_unsupervised.py',
   'PYMODULE-1'),
  ('sklearn.pipeline',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\pipeline.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._data',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_data.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._discretization',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_discretization.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._encoders',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_encoders.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._function_transformer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_function_transformer.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._label',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_label.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._polynomial',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_polynomial.py',
   'PYMODULE-1'),
  ('sklearn.preprocessing._target_encoder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder.py',
   'PYMODULE-1'),
  ('sklearn.svm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.svm._base',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_base.py',
   'PYMODULE-1'),
  ('sklearn.svm._bounds',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_bounds.py',
   'PYMODULE-1'),
  ('sklearn.svm._classes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_classes.py',
   'PYMODULE-1'),
  ('sklearn.tree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.tree._classes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_classes.py',
   'PYMODULE-1'),
  ('sklearn.tree._export',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_export.py',
   'PYMODULE-1'),
  ('sklearn.tree._reingold_tilford',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_reingold_tilford.py',
   'PYMODULE-1'),
  ('sklearn.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\__init__.py',
   'PYMODULE-1'),
  ('sklearn.utils._arpack',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_arpack.py',
   'PYMODULE-1'),
  ('sklearn.utils._array_api',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_array_api.py',
   'PYMODULE-1'),
  ('sklearn.utils._available_if',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_available_if.py',
   'PYMODULE-1'),
  ('sklearn.utils._bunch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_bunch.py',
   'PYMODULE-1'),
  ('sklearn.utils._encode',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_encode.py',
   'PYMODULE-1'),
  ('sklearn.utils._estimator_html_repr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_estimator_html_repr.py',
   'PYMODULE-1'),
  ('sklearn.utils._joblib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_joblib.py',
   'PYMODULE-1'),
  ('sklearn.utils._mask',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_mask.py',
   'PYMODULE-1'),
  ('sklearn.utils._metadata_requests',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_metadata_requests.py',
   'PYMODULE-1'),
  ('sklearn.utils._param_validation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_param_validation.py',
   'PYMODULE-1'),
  ('sklearn.utils._plotting',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_plotting.py',
   'PYMODULE-1'),
  ('sklearn.utils._pprint',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_pprint.py',
   'PYMODULE-1'),
  ('sklearn.utils._response',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_response.py',
   'PYMODULE-1'),
  ('sklearn.utils._set_output',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_set_output.py',
   'PYMODULE-1'),
  ('sklearn.utils._show_versions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_show_versions.py',
   'PYMODULE-1'),
  ('sklearn.utils._tags',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_tags.py',
   'PYMODULE-1'),
  ('sklearn.utils._testing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_testing.py',
   'PYMODULE-1'),
  ('sklearn.utils.class_weight',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\class_weight.py',
   'PYMODULE-1'),
  ('sklearn.utils.deprecation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\deprecation.py',
   'PYMODULE-1'),
  ('sklearn.utils.discovery',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\discovery.py',
   'PYMODULE-1'),
  ('sklearn.utils.extmath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\extmath.py',
   'PYMODULE-1'),
  ('sklearn.utils.fixes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\fixes.py',
   'PYMODULE-1'),
  ('sklearn.utils.graph',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\graph.py',
   'PYMODULE-1'),
  ('sklearn.utils.metadata_routing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\metadata_routing.py',
   'PYMODULE-1'),
  ('sklearn.utils.metaestimators',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\metaestimators.py',
   'PYMODULE-1'),
  ('sklearn.utils.multiclass',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\multiclass.py',
   'PYMODULE-1'),
  ('sklearn.utils.optimize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\optimize.py',
   'PYMODULE-1'),
  ('sklearn.utils.parallel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\parallel.py',
   'PYMODULE-1'),
  ('sklearn.utils.random',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\random.py',
   'PYMODULE-1'),
  ('sklearn.utils.sparsefuncs',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs.py',
   'PYMODULE-1'),
  ('sklearn.utils.stats',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\stats.py',
   'PYMODULE-1'),
  ('sklearn.utils.validation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\validation.py',
   'PYMODULE-1'),
  ('socket',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\socket.py',
   'PYMODULE-1'),
  ('socketserver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\socketserver.py',
   'PYMODULE-1'),
  ('sqlite3',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\sqlite3\\__init__.py',
   'PYMODULE-1'),
  ('sqlite3.dbapi2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-1'),
  ('sqlite3.dump',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\sqlite3\\dump.py',
   'PYMODULE-1'),
  ('ssl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\ssl.py',
   'PYMODULE-1'),
  ('statistics',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\statistics.py',
   'PYMODULE-1'),
  ('string',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\string.py',
   'PYMODULE-1'),
  ('stringprep',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\stringprep.py',
   'PYMODULE-1'),
  ('subprocess',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\subprocess.py',
   'PYMODULE-1'),
  ('sysconfig',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\sysconfig.py',
   'PYMODULE-1'),
  ('tarfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tarfile.py',
   'PYMODULE-1'),
  ('tempfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tempfile.py',
   'PYMODULE-1'),
  ('textwrap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\textwrap.py',
   'PYMODULE-1'),
  ('threading',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\threading.py',
   'PYMODULE-1'),
  ('threadpoolctl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE-1'),
  ('timeit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\timeit.py',
   'PYMODULE-1'),
  ('token',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\token.py',
   'PYMODULE-1'),
  ('tokenize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tokenize.py',
   'PYMODULE-1'),
  ('tomllib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tomllib\\__init__.py',
   'PYMODULE-1'),
  ('tomllib._parser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tomllib\\_parser.py',
   'PYMODULE-1'),
  ('tomllib._re',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tomllib\\_re.py',
   'PYMODULE-1'),
  ('tomllib._types',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tomllib\\_types.py',
   'PYMODULE-1'),
  ('tqdm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE-1'),
  ('tqdm._dist_ver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE-1'),
  ('tqdm._monitor',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE-1'),
  ('tqdm._tqdm_pandas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE-1'),
  ('tqdm.cli',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE-1'),
  ('tqdm.gui',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE-1'),
  ('tqdm.notebook',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE-1'),
  ('tqdm.std',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE-1'),
  ('tqdm.utils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE-1'),
  ('tqdm.version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE-1'),
  ('tracemalloc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tracemalloc.py',
   'PYMODULE-1'),
  ('tty',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\tty.py',
   'PYMODULE-1'),
  ('typing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\typing.py',
   'PYMODULE-1'),
  ('typing_extensions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-1'),
  ('ui', 'D:\\NK_Python\\脑机接口康复训练\\ui\\__init__.py', 'PYMODULE-1'),
  ('ui.components',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\__init__.py',
   'PYMODULE-1'),
  ('ui.components.chart_widgets',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\chart_widgets.py',
   'PYMODULE-1'),
  ('ui.components.filter_panel',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\filter_panel.py',
   'PYMODULE-1'),
  ('ui.components.interactive_chart',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\interactive_chart.py',
   'PYMODULE-1'),
  ('ui.components.mne_topography_widget',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\mne_topography_widget.py',
   'PYMODULE-1'),
  ('ui.components.modern_card',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\modern_card.py',
   'PYMODULE-1'),
  ('ui.components.no_wheel_widgets',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\no_wheel_widgets.py',
   'PYMODULE-1'),
  ('ui.components.parameter_adjuster',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\parameter_adjuster.py',
   'PYMODULE-1'),
  ('ui.components.pyqtgraph_curves_widget',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\pyqtgraph_curves_widget.py',
   'PYMODULE-1'),
  ('ui.components.realtime_metrics_panel',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\realtime_metrics_panel.py',
   'PYMODULE-1'),
  ('ui.components.sidebar',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\sidebar.py',
   'PYMODULE-1'),
  ('ui.components.stats_card',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\stats_card.py',
   'PYMODULE-1'),
  ('ui.components.status_indicator',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\status_indicator.py',
   'PYMODULE-1'),
  ('ui.components.themed_message_box',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\themed_message_box.py',
   'PYMODULE-1'),
  ('ui.components.topbar',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\components\\topbar.py',
   'PYMODULE-1'),
  ('ui.dialogs', '-', 'PYMODULE-1'),
  ('ui.dialogs.activation_dialog',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\dialogs\\activation_dialog.py',
   'PYMODULE-1'),
  ('ui.login_window',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\login_window.py',
   'PYMODULE-1'),
  ('ui.main_window',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\main_window.py',
   'PYMODULE-1'),
  ('ui.pages', 'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\__init__.py', 'PYMODULE-1'),
  ('ui.pages.base_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\base_page.py',
   'PYMODULE-1'),
  ('ui.pages.patients_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\patients_page.py',
   'PYMODULE-1'),
  ('ui.pages.reports_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\reports_page.py',
   'PYMODULE-1'),
  ('ui.pages.settings_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\settings_page.py',
   'PYMODULE-1'),
  ('ui.pages.treatment_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\treatment_page.py',
   'PYMODULE-1'),
  ('ui.pages.users_page',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\pages\\users_page.py',
   'PYMODULE-1'),
  ('ui.themes',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\themes\\__init__.py',
   'PYMODULE-1'),
  ('ui.themes.theme_manager',
   'D:\\NK_Python\\脑机接口康复训练\\ui\\themes\\theme_manager.py',
   'PYMODULE-1'),
  ('unittest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\__init__.py',
   'PYMODULE-1'),
  ('unittest._log',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\_log.py',
   'PYMODULE-1'),
  ('unittest.async_case',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\async_case.py',
   'PYMODULE-1'),
  ('unittest.case',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\case.py',
   'PYMODULE-1'),
  ('unittest.loader',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\loader.py',
   'PYMODULE-1'),
  ('unittest.main',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\main.py',
   'PYMODULE-1'),
  ('unittest.mock',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\mock.py',
   'PYMODULE-1'),
  ('unittest.result',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\result.py',
   'PYMODULE-1'),
  ('unittest.runner',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\runner.py',
   'PYMODULE-1'),
  ('unittest.signals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\signals.py',
   'PYMODULE-1'),
  ('unittest.suite',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\suite.py',
   'PYMODULE-1'),
  ('unittest.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\unittest\\util.py',
   'PYMODULE-1'),
  ('urllib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\urllib\\__init__.py',
   'PYMODULE-1'),
  ('urllib.error',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\urllib\\error.py',
   'PYMODULE-1'),
  ('urllib.parse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\urllib\\parse.py',
   'PYMODULE-1'),
  ('urllib.request',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\urllib\\request.py',
   'PYMODULE-1'),
  ('urllib.response',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\urllib\\response.py',
   'PYMODULE-1'),
  ('urllib3',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-1'),
  ('urllib3._base_connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-1'),
  ('urllib3._collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-1'),
  ('urllib3._request_methods',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-1'),
  ('urllib3._version',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-1'),
  ('urllib3.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-1'),
  ('urllib3.connectionpool',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-1'),
  ('urllib3.contrib',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.fetch',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.request',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.response',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-1'),
  ('urllib3.contrib.pyopenssl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-1'),
  ('urllib3.contrib.socks',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-1'),
  ('urllib3.exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-1'),
  ('urllib3.fields',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-1'),
  ('urllib3.filepost',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-1'),
  ('urllib3.http2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.http2.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-1'),
  ('urllib3.http2.probe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-1'),
  ('urllib3.poolmanager',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-1'),
  ('urllib3.response',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-1'),
  ('urllib3.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.util.connection',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-1'),
  ('urllib3.util.proxy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-1'),
  ('urllib3.util.request',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-1'),
  ('urllib3.util.response',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-1'),
  ('urllib3.util.retry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-1'),
  ('urllib3.util.ssl_',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-1'),
  ('urllib3.util.ssl_match_hostname',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-1'),
  ('urllib3.util.ssltransport',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-1'),
  ('urllib3.util.timeout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-1'),
  ('urllib3.util.url',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-1'),
  ('urllib3.util.util',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-1'),
  ('urllib3.util.wait',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-1'),
  ('utils', 'D:\\NK_Python\\脑机接口康复训练\\utils\\__init__.py', 'PYMODULE-1'),
  ('utils.chart_helpers',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\chart_helpers.py',
   'PYMODULE-1'),
  ('utils.db_helpers',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\db_helpers.py',
   'PYMODULE-1'),
  ('utils.path_manager',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\path_manager.py',
   'PYMODULE-1'),
  ('utils.simple_permission_manager',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\simple_permission_manager.py',
   'PYMODULE-1'),
  ('utils.user_helpers',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\user_helpers.py',
   'PYMODULE-1'),
  ('utils.warning_manager',
   'D:\\NK_Python\\脑机接口康复训练\\utils\\warning_manager.py',
   'PYMODULE-1'),
  ('uuid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\uuid.py',
   'PYMODULE-1'),
  ('webbrowser',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\webbrowser.py',
   'PYMODULE-1'),
  ('winrt', '-', 'PYMODULE-1'),
  ('winrt.runtime',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\runtime\\__init__.py',
   'PYMODULE-1'),
  ('winrt.runtime._internals',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\runtime\\_internals.py',
   'PYMODULE-1'),
  ('winrt.system',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\system\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows', '-', 'PYMODULE-1'),
  ('winrt.windows.devices', '-', 'PYMODULE-1'),
  ('winrt.windows.devices.bluetooth',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\devices\\bluetooth\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.devices.bluetooth.advertisement',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\devices\\bluetooth\\advertisement\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.devices.bluetooth.genericattributeprofile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\devices\\bluetooth\\genericattributeprofile\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.devices.enumeration',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\devices\\enumeration\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.foundation',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\foundation\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.foundation.collections',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\foundation\\collections\\__init__.py',
   'PYMODULE-1'),
  ('winrt.windows.storage', '-', 'PYMODULE-1'),
  ('winrt.windows.storage.streams',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\windows\\storage\\streams\\__init__.py',
   'PYMODULE-1'),
  ('xml',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom.NodeFilter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-1'),
  ('xml.dom.domreg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE-1'),
  ('xml.dom.expatbuilder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-1'),
  ('xml.dom.minicompat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE-1'),
  ('xml.dom.minidom',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE-1'),
  ('xml.dom.pulldom',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE-1'),
  ('xml.dom.xmlbuilder',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-1'),
  ('xml.etree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-1'),
  ('xml.etree.ElementInclude',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-1'),
  ('xml.etree.ElementPath',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-1'),
  ('xml.etree.ElementTree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-1'),
  ('xml.etree.cElementTree',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-1'),
  ('xml.parsers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('xml.parsers.expat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-1'),
  ('xml.sax',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE-1'),
  ('xml.sax._exceptions',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-1'),
  ('xml.sax.expatreader',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-1'),
  ('xml.sax.handler',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-1'),
  ('xml.sax.saxutils',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-1'),
  ('xml.sax.xmlreader',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-1'),
  ('xmlrpc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE-1'),
  ('xmlrpc.client',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\xmlrpc\\client.py',
   'PYMODULE-1'),
  ('zipfile',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\zipfile.py',
   'PYMODULE-1'),
  ('zipimport',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\zipimport.py',
   'PYMODULE-1'),
  ('zstandard',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE-1'),
  ('zstandard.backend_cffi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE-1')])
