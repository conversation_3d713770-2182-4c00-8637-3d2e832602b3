([('脑机接口康复训练系统.exe',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\脑机接口康复训练系统.exe',
   'EXECUTABLE'),
  ('libs\\RecoveryDLL.dll',
   'D:\\NK_Python\\脑机接口康复训练\\libs\\RecoveryDLL.dll',
   'BINARY'),
  ('sklearn\\.libs\\vcomp140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\.libs\\vcomp140.dll',
   'BINARY'),
  ('sklearn\\.libs\\msvcp140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\.libs\\msvcp140.dll',
   'BINARY'),
  ('python311.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\python311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('matplotlib.libs\\msvcp140-456d948669199b545d061b84c160bebc.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib.libs\\msvcp140-456d948669199b545d061b84c160bebc.dll',
   'BINARY'),
  ('scipy.libs\\libopenblas_v0.3.20-571-g3dec11c6-gcc_10_3_0-c2315440d6b6cef5037bad648efc8c59.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy.libs\\libopenblas_v0.3.20-571-g3dec11c6-gcc_10_3_0-c2315440d6b6cef5037bad648efc8c59.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlite.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlite.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlpsql.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlpsql.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlmimer.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlmimer.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlodbc.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlodbc.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('_decimal.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_constants.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_highs\\_highs_constants.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_wrapper.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_highs\\_highs_wrapper.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_comb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_interpolative.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_interpolative.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\__nnls.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\__nnls.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_slsqp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_cobyla.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack2.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\optimize\\_minpack2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\ft2font.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\kiwisolver\\_cext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_tri.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\contourpy\\_contourpy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\reduction.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_isfinite.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_cython_blas.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\murmurhash.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\arrayfuncs.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_criterion.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_splitter.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_quad_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_vector_sentinel.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_sorting.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_heap.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_heap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_dist_metrics.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_logistic_sigmoid.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_logistic_sigmoid.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_random.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_random.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_kd_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_ball_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\interpnd.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\interpnd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_bspl.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\dfitpack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\dfitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_mio5_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio5_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_streams.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_streams.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\io\\matlab\\_mio_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\io\\matlab\\_mio_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_weight_vector.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_cd_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtSvgWidgets.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtSvgWidgets.pyd',
   'EXTENSION'),
  ('PySide6\\QtSvg.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtSvg.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_openmp_helpers.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_isotonic.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_isotonic.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_common.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_optimal_leaf_ordering.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\_optimal_leaf_ordering.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_hierarchy.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\_hierarchy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_vq.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\cluster\\_vq.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_fast_dict.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_seq_dataset.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_typedefs.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_loss\\_loss.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\ensemble\\_gradient_boosting.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_liblinear.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sag_fast.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\__check_build\\_check_build.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_avif.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_foundation.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_foundation.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_foundation_collections.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_foundation_collections.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_devices_bluetooth_advertisement.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_devices_bluetooth_advertisement.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_storage_streams.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_storage_streams.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_devices_bluetooth.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_devices_bluetooth.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_devices_enumeration.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_devices_enumeration.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('winrt\\_winrt_windows_devices_bluetooth_genericattributeprofile.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\_winrt_windows_devices_bluetooth_genericattributeprofile.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtTest.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtTest.pyd',
   'EXTENSION'),
  ('PySide6\\QtOpenGLWidgets.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtOpenGLWidgets.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\_sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flinalg.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_flinalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_mvn.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_statlib.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_statlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\invgauss_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\invgauss_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\skewnorm_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\skewnorm_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nct_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\nct_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncx2_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\ncx2_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncf_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\ncf_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\hypergeom_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\hypergeom_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nbinom_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\nbinom_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\binom_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\binom_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\beta_ufunc.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_boost\\beta_ufunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_peak_finding_utils.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_peak_finding_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_spectral.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_spectral.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sosfilt.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_sosfilt.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_spline.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_spline.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_upfirdn_apply.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_upfirdn_apply.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_max_len_seq_inner.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_max_len_seq_inner.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sigtools.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\signal\\_sigtools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_isolve\\_iterative.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_iterative.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtSql.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtSql.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('MSVCR100.dll', 'C:\\windows\\system32\\MSVCR100.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Sql.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Sql.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('liblzma.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('libexpat.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('VCOMP140.DLL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\VCOMP140.DLL',
   'BINARY'),
  ('python3.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\Qt6SvgWidgets.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6SvgWidgets.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('sqlite3.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('winrt\\MSVCP140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\winrt\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Test.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Test.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGLWidgets.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6OpenGLWidgets.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\ucrtbase.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6-6.6.1.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\INSTALLER',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\LicenseRef-Qt-Commercial.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\LicenseRef-Qt-Commercial.txt',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\METADATA',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\RECORD',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\REQUESTED',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\WHEEL',
   'DATA'),
  ('PySide6-6.6.1.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6-6.6.1.dist-info\\top_level.txt',
   'DATA'),
  ('ShuJu.db', 'D:\\NK_Python\\脑机接口康复训练\\ShuJu.db', 'DATA'),
  ('config\\.activation',
   'D:\\NK_Python\\脑机接口康复训练\\config\\.activation',
   'DATA'),
  ('config\\__pycache__\\__init__.cpython-39.pyc',
   'D:\\NK_Python\\脑机接口康复训练\\config\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('config\\__pycache__\\database.cpython-39.pyc',
   'D:\\NK_Python\\脑机接口康复训练\\config\\__pycache__\\database.cpython-39.pyc',
   'DATA'),
  ('config\\__pycache__\\settings.cpython-39.pyc',
   'D:\\NK_Python\\脑机接口康复训练\\config\\__pycache__\\settings.cpython-39.pyc',
   'DATA'),
  ('config\\__pycache__\\themes.cpython-39.pyc',
   'D:\\NK_Python\\脑机接口康复训练\\config\\__pycache__\\themes.cpython-39.pyc',
   'DATA'),
  ('config\\classifiers_optimized.json',
   'D:\\NK_Python\\脑机接口康复训练\\config\\classifiers_optimized.json',
   'DATA'),
  ('config\\feature_extraction_optimized.json',
   'D:\\NK_Python\\脑机接口康复训练\\config\\feature_extraction_optimized.json',
   'DATA'),
  ('config\\feature_extraction_optimized_8_30Hz.json',
   'D:\\NK_Python\\脑机接口康复训练\\config\\feature_extraction_optimized_8_30Hz.json',
   'DATA'),
  ('config\\settings.json',
   'D:\\NK_Python\\脑机接口康复训练\\config\\settings.json',
   'DATA'),
  ('config\\users.json', 'D:\\NK_Python\\脑机接口康复训练\\config\\users.json', 'DATA'),
  ('config\\weighted_voting_config.json',
   'D:\\NK_Python\\脑机接口康复训练\\config\\weighted_voting_config.json',
   'DATA'),
  ('data\\features\\112233_latest_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_latest_plv.pkl',
   'DATA'),
  ('data\\features\\112233_latest_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_latest_riemannian.pkl',
   'DATA'),
  ('data\\features\\112233_latest_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_latest_tangent_space.pkl',
   'DATA'),
  ('data\\features\\112233_latest_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_latest_tef.pkl',
   'DATA'),
  ('data\\features\\112233_round_1_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_round_1_plv.pkl',
   'DATA'),
  ('data\\features\\112233_round_1_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_round_1_riemannian.pkl',
   'DATA'),
  ('data\\features\\112233_round_1_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_round_1_tangent_space.pkl',
   'DATA'),
  ('data\\features\\112233_round_1_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\112233_round_1_tef.pkl',
   'DATA'),
  ('data\\features\\33_latest_fbcsp.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_latest_fbcsp.pkl',
   'DATA'),
  ('data\\features\\33_latest_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_latest_plv.pkl',
   'DATA'),
  ('data\\features\\33_latest_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_latest_riemannian.pkl',
   'DATA'),
  ('data\\features\\33_latest_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_latest_tangent_space.pkl',
   'DATA'),
  ('data\\features\\33_latest_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_latest_tef.pkl',
   'DATA'),
  ('data\\features\\33_round_1_fbcsp.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_1_fbcsp.pkl',
   'DATA'),
  ('data\\features\\33_round_1_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_1_plv.pkl',
   'DATA'),
  ('data\\features\\33_round_1_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_1_riemannian.pkl',
   'DATA'),
  ('data\\features\\33_round_1_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_1_tangent_space.pkl',
   'DATA'),
  ('data\\features\\33_round_1_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_1_tef.pkl',
   'DATA'),
  ('data\\features\\33_round_2_fbcsp.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_2_fbcsp.pkl',
   'DATA'),
  ('data\\features\\33_round_2_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_2_plv.pkl',
   'DATA'),
  ('data\\features\\33_round_2_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_2_riemannian.pkl',
   'DATA'),
  ('data\\features\\33_round_2_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_2_tangent_space.pkl',
   'DATA'),
  ('data\\features\\33_round_2_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_2_tef.pkl',
   'DATA'),
  ('data\\features\\33_round_3_fbcsp.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_3_fbcsp.pkl',
   'DATA'),
  ('data\\features\\33_round_3_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_3_plv.pkl',
   'DATA'),
  ('data\\features\\33_round_3_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_3_riemannian.pkl',
   'DATA'),
  ('data\\features\\33_round_3_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_3_tangent_space.pkl',
   'DATA'),
  ('data\\features\\33_round_3_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_3_tef.pkl',
   'DATA'),
  ('data\\features\\33_round_4_fbcsp.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_4_fbcsp.pkl',
   'DATA'),
  ('data\\features\\33_round_4_plv.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_4_plv.pkl',
   'DATA'),
  ('data\\features\\33_round_4_riemannian.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_4_riemannian.pkl',
   'DATA'),
  ('data\\features\\33_round_4_tangent_space.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_4_tangent_space.pkl',
   'DATA'),
  ('data\\features\\33_round_4_tef.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\features\\33_round_4_tef.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_report_112233_latest.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_112233_latest.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_112233_round_1.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_112233_round_1.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_33_latest.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_33_latest.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_33_round_1.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_33_round_1.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_33_round_2.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_33_round_2.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_33_round_3.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_33_round_3.txt',
   'DATA'),
  ('data\\models\\weighted_voting_report_33_round_4.txt',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_report_33_round_4.txt',
   'DATA'),
  ('data\\models\\weighted_voting_system_112233_latest.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_112233_latest.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_112233_round_1.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_112233_round_1.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_33_latest.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_33_latest.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_33_round_1.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_33_round_1.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_33_round_2.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_33_round_2.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_33_round_3.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_33_round_3.pkl',
   'DATA'),
  ('data\\models\\weighted_voting_system_33_round_4.pkl',
   'D:\\NK_Python\\脑机接口康复训练\\data\\models\\weighted_voting_system_33_round_4.pkl',
   'DATA'),
  ('data\\raw_training_data\\112233_round_1_data.npz',
   'D:\\NK_Python\\脑机接口康复训练\\data\\raw_training_data\\112233_round_1_data.npz',
   'DATA'),
  ('data\\raw_training_data\\33_round_1_data.npz',
   'D:\\NK_Python\\脑机接口康复训练\\data\\raw_training_data\\33_round_1_data.npz',
   'DATA'),
  ('data\\raw_training_data\\33_round_2_data.npz',
   'D:\\NK_Python\\脑机接口康复训练\\data\\raw_training_data\\33_round_2_data.npz',
   'DATA'),
  ('data\\raw_training_data\\33_round_3_data.npz',
   'D:\\NK_Python\\脑机接口康复训练\\data\\raw_training_data\\33_round_3_data.npz',
   'DATA'),
  ('data\\raw_training_data\\33_round_4_data.npz',
   'D:\\NK_Python\\脑机接口康复训练\\data\\raw_training_data\\33_round_4_data.npz',
   'DATA'),
  ('data\\training_history\\112233_training_history.json',
   'D:\\NK_Python\\脑机接口康复训练\\data\\training_history\\112233_training_history.json',
   'DATA'),
  ('data\\training_history\\33_training_history.json',
   'D:\\NK_Python\\脑机接口康复训练\\data\\training_history\\33_training_history.json',
   'DATA'),
  ('icons\\ht.png', 'D:\\NK_Python\\脑机接口康复训练\\icons\\ht.png', 'DATA'),
  ('jaraco.text-4.0.0.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\LICENSE',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\LICENSE',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\METADATA',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\RECORD',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\WHEEL',
   'DATA'),
  ('jaraco.text-4.0.0.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco.text-4.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('libs\\RecoveryDLL.lib',
   'D:\\NK_Python\\脑机接口康复训练\\libs\\RecoveryDLL.lib',
   'DATA'),
  ('libs\\RecoveryDLLd.lib',
   'D:\\NK_Python\\脑机接口康复训练\\libs\\RecoveryDLLd.lib',
   'DATA'),
  ('libs\\RecoveryModuleDLL.h',
   'D:\\NK_Python\\脑机接口康复训练\\libs\\RecoveryModuleDLL.h',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\DELVEWHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\INSTALLER',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_AMSFONTS',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_AMSFONTS',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_BAKOMA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_BAKOMA',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_CARLOGO',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_CARLOGO',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_COLORBREWER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_COLORBREWER',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_COURIERTEN',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_COURIERTEN',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_JSXTOOLS_RESIZE_OBSERVER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_JSXTOOLS_RESIZE_OBSERVER',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_QHULL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_QHULL',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_QT4_EDITOR',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_QT4_EDITOR',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_SOLARIZED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_SOLARIZED',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_STIX',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_STIX',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\LICENSE_YORICK',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\LICENSE_YORICK',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\METADATA',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\RECORD',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\REQUESTED',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\WHEEL',
   'DATA'),
  ('matplotlib-3.8.4.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib-3.8.4.dist-info\\top_level.txt',
   'DATA'),
  ('mne-1.5.1.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('mne-1.5.1.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('mne-1.5.1.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\METADATA',
   'DATA'),
  ('mne-1.5.1.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\RECORD',
   'DATA'),
  ('mne-1.5.1.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('mne-1.5.1.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\WHEEL',
   'DATA'),
  ('mne-1.5.1.dist-info\\entry_points.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('mne-1.5.1.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne-1.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-1.26.4.dist-info\\DELVEWHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-1.26.4.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-1.26.4.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-1.26.4.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\METADATA',
   'DATA'),
  ('numpy-1.26.4.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\RECORD',
   'DATA'),
  ('numpy-1.26.4.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-1.26.4.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\WHEEL',
   'DATA'),
  ('numpy-1.26.4.dist-info\\entry_points.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\numpy-1.26.4.dist-info\\entry_points.txt',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\INSTALLER',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\LICENSE.txt',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\METADATA',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\RECORD',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\REQUESTED',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\WHEEL',
   'DATA'),
  ('pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pyqtgraph-0.13.7.dist-info\\top_level.txt',
   'DATA'),
  ('scipy-1.11.4.dist-info\\DELVEWHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('scipy-1.11.4.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\INSTALLER',
   'DATA'),
  ('scipy-1.11.4.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('scipy-1.11.4.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\METADATA',
   'DATA'),
  ('scipy-1.11.4.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\RECORD',
   'DATA'),
  ('scipy-1.11.4.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\REQUESTED',
   'DATA'),
  ('scipy-1.11.4.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\scipy-1.11.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\PKG-INFO',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\PKG-INFO',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\SOURCES.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\SOURCES.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\dependency_links.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\dependency_links.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\entry_points.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\entry_points.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\requires.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\requires.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.11.egg-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools-78.1.1-py3.11.egg-info\\top_level.txt',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\INSTALLER',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\LicenseRef-Qt-Commercial.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\LicenseRef-Qt-Commercial.txt',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\METADATA',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\RECORD',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\REQUESTED',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\WHEEL',
   'DATA'),
  ('shiboken6-6.6.1.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\shiboken6-6.6.1.dist-info\\top_level.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('certifi\\py.typed',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('jaraco\\text\\Lorem ipsum.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\wine_data.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\wine_data.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\tree\\_tree.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_tree.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\images\\china.jpg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\images\\china.jpg',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\lfw.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\lfw.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\california_housing.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\california_housing.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\iris.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\iris.csv',
   'DATA'),
  ('sklearn\\datasets\\descr\\rcv1.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\rcv1.rst',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd',
   'DATA'),
  ('sklearn\\tree\\_utils.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_utils.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pxd',
   'DATA'),
  ('sklearn\\datasets\\descr\\linnerud.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\linnerud.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'DATA'),
  ('sklearn\\datasets\\descr\\wine_data.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\wine_data.rst',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\breast_cancer.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\breast_cancer.rst',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\diabetes.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\diabetes.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\olivetti_faces.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\olivetti_faces.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\covtype.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\covtype.rst',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_physiological.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_physiological.csv',
   'DATA'),
  ('sklearn\\tree\\_splitter.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_splitter.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_exercise.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_exercise.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\digits.csv.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\digits.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\breast_cancer.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\breast_cancer.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\iris.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\iris.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\images\\flower.jpg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\images\\flower.jpg',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd',
   'DATA'),
  ('sklearn\\utils\\_random.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_random.pxd',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\boston_house_prices.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\boston_house_prices.csv',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.pxd',
   'DATA'),
  ('sklearn\\datasets\\descr\\kddcup99.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\kddcup99.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\_loss\\_loss.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\_loss\\_loss.pxd',
   'DATA'),
  ('sklearn\\datasets\\descr\\digits.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\digits.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\images\\README.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\images\\README.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pxd',
   'DATA'),
  ('sklearn\\utils\\_heap.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_heap.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'DATA'),
  ('sklearn\\tree\\_criterion.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\tree\\_criterion.pxd',
   'DATA'),
  ('sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'DATA'),
  ('sklearn\\utils\\_sorting.pxd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\utils\\_sorting.pxd',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\REQUESTED',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-208_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-208_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-UMD-2_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-UMD-2_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-160.lay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-160.lay',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-NYU-2019_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-NYU-2019_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\biosemi16_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\biosemi16_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycap128ch-avg_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycap128ch-avg_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\ctf151_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\ctf151_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-AD.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-AD.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\easycap-M43.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\easycap-M43.txt',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi64.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi64.txt',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\neuromag306mag_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\neuromag306mag_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\EEG1005.lay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\EEG1005.lay',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\neuromag122cmb_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\neuromag122cmb_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-AS-2008.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-AS-2008.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_alphabetic.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_alphabetic.elc',
   'DATA'),
  ('mne\\channels\\data\\layouts\\CTF-275.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\CTF-275.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_postfixed.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_postfixed.elc',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\bti148_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\bti148_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\CTF275.lay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\CTF275.lay',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\biosemi64_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\biosemi64_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\ecog256_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\ecog256_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-157.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-157.lout',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycapM14_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycapM14_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\EGI256.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\EGI256.lout',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\yokogawa440_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\yokogawa440_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\language29ch-avg_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\language29ch-avg_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\itab28_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\itab28_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\yokogawa160_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\yokogawa160_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-64_1.0.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-64_1.0.sfp',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\elec1010_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\elec1010_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-125.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-125.lout',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\ctf64_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\ctf64_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_prefixed.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_prefixed.elc',
   'DATA'),
  ('mne\\channels\\data\\montages\\artinis-brite23.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\artinis-brite23.elc',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-32.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-32.sfp',
   'DATA'),
  ('mne\\channels\\data\\layouts\\Neuromag_122.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\Neuromag_122.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi256.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi256.txt',
   'DATA'),
  ('mne\\channels\\data\\layouts\\biosemi.lay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\biosemi.lay',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\neuromag306planar_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\neuromag306planar_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\CTF151.lay',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\CTF151.lay',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\ctf275_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\ctf275_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\bti248grad_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\bti248grad_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\Vectorview-grad_norm.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\Vectorview-grad_norm.lout',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-UMD-4_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-UMD-4_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\mpi_59_channels_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\mpi_59_channels_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycap64ch-avg_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycap64ch-avg_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-257.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-257.sfp',
   'DATA'),
  ('mne\\channels\\data\\montages\\brainproducts-RNP-BA-128.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\brainproducts-RNP-BA-128.txt',
   'DATA'),
  ('mne\\channels\\data\\montages\\mgh70.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\mgh70.elc',
   'DATA'),
  ('mne\\channels\\data\\layouts\\Vectorview-grad.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\Vectorview-grad.lout',
   'DATA'),
  ('mne\\channels\\data\\layouts\\GeodesicHeadWeb-130.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\GeodesicHeadWeb-130.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\easycap-M10.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\easycap-M10.txt',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-256.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-256.sfp',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycapM1_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycapM1_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\mgh60.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\mgh60.elc',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycapM15_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycapM15_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\GeodesicHeadWeb-280.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\GeodesicHeadWeb-280.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_1005.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_1005.elc',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\biosemi32_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\biosemi32_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\elec1020_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\elec1020_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\Vectorview-mag.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\Vectorview-mag.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-129.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-129.sfp',
   'DATA'),
  ('mne\\channels\\data\\montages\\easycap-M1.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\easycap-M1.txt',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-157_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-157_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\eeg1010_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\eeg1010_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\ecog256bipolar_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\ecog256bipolar_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi160.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi160.txt',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-UMD-1_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-UMD-1_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\KIT-UMD-3_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\KIT-UMD-3_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi128.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi128.txt',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycap32ch-avg_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycap32ch-avg_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\easycapM11_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\easycapM11_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi16.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi16.txt',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\itab153_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\itab153_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\elec1005_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\elec1005_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\bti248_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\bti248_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\layouts\\Vectorview-all.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\Vectorview-all.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\artinis-octamon.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\artinis-octamon.elc',
   'DATA'),
  ('mne\\channels\\data\\layouts\\magnesWH3600.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\magnesWH3600.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_1020.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_1020.elc',
   'DATA'),
  ('mne\\channels\\data\\neighbors\\neuromag306cmb_neighb.mat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\neighbors\\neuromag306cmb_neighb.mat',
   'DATA'),
  ('mne\\channels\\data\\montages\\EGI_256.csd',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\EGI_256.csd',
   'DATA'),
  ('mne\\channels\\data\\montages\\biosemi32.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\biosemi32.txt',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-65_1.0.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-65_1.0.sfp',
   'DATA'),
  ('mne\\channels\\data\\layouts\\KIT-UMD-3.lout',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\layouts\\KIT-UMD-3.lout',
   'DATA'),
  ('mne\\channels\\data\\montages\\standard_primed.elc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\standard_primed.elc',
   'DATA'),
  ('mne\\channels\\data\\montages\\GSN-HydroCel-128.sfp',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\mne\\channels\\data\\montages\\GSN-HydroCel-128.sfp',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'G:\\ProgramData\\anaconda3\\envs\\nk_pip_clean\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'D:\\NK_Python\\脑机接口康复训练\\build\\脑机接口康复训练系统\\base_library.zip',
   'DATA')],)
