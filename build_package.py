#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑机接口康复训练系统 - 最终打包脚本
BCI Rehabilitation Training System - Final Build Script

解决所有已知问题：
1. PySide6模块缺失
2. MNE unittest依赖
3. 文件路径问题
4. 实时曲线PyQtGraph支持
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_dependencies():
    """检查关键依赖"""
    print("检查关键依赖...")
    
    deps = {
        'PySide6': 'PySide6',
        'mne': 'mne',
        'pyqtgraph': 'pyqtgraph',
        'numpy': 'numpy',
        'scipy': 'scipy',
        'matplotlib': 'matplotlib',
        'setuptools': 'setuptools'
    }
    
    missing = []
    for import_name, package_name in deps.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {package_name}: {version}")
        except ImportError:
            print(f"✗ {package_name}: 缺失")
            missing.append(package_name)
    
    if missing:
        print(f"请安装缺失的包: pip install {' '.join(missing)}")
        return False
    
    return True




def clean_build():
    """清理构建文件"""
    print("清理构建文件...")

    dirs_to_clean = ["dist", "build", "package_output"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理: {dir_name}")

    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✓ 已清理: {spec_file}")


def create_size_optimization_hook():
    """创建大小优化的PyInstaller hook文件"""
    print("创建大小优化hook文件...")

    # 创建hooks目录
    hooks_dir = Path("hooks")
    hooks_dir.mkdir(exist_ok=True)

    # 创建MNE优化hook
    mne_hook = hooks_dir / "hook-mne.py"
    with open(mne_hook, 'w', encoding='utf-8') as f:
        f.write("""# MNE大小优化hook
from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 只收集必要的MNE模块
hiddenimports = [
    'mne.io',
    'mne.channels',
    'mne.viz.topomap',
    'mne.utils',
    'mne.preprocessing',
    'mne.filter'
]

# 收集必要的数据文件（电极位置数据）
datas = collect_data_files('mne.channels.data', include_py_files=False)
""")

    # 创建PyQtGraph优化hook
    pyqtgraph_hook = hooks_dir / "hook-pyqtgraph.py"
    with open(pyqtgraph_hook, 'w', encoding='utf-8') as f:
        f.write("""# PyQtGraph大小优化hook
from PyInstaller.utils.hooks import collect_submodules

# 只收集必要的PyQtGraph模块
hiddenimports = [
    'pyqtgraph.graphicsItems',
    'pyqtgraph.widgets',
    'pyqtgraph.Qt',
    'pyqtgraph.functions',
    'pyqtgraph.Point',
    'pyqtgraph.Vector'
]
""")

    print(f"✓ 已创建优化hook文件: {hooks_dir}")
    return hooks_dir


def build_package():
    """构建软件包 - 大小优化版本"""
    print("开始构建大小优化软件包...")

    # 创建优化hook文件
    hooks_dir = create_size_optimization_hook()

    cmd = [
        sys.executable, "-O", "-m", "PyInstaller",  # 添加字节码优化
        "--name=脑机接口康复训练系统",
        "--windowed",  # 不显示控制台窗口
        "--onedir",
        "--noconfirm",
        "--clean",
        "--icon=icons/ht.png",  # 设置程序图标
        "--distpath=package_output",
        "--workpath=build",
        "--specpath=.",
        f"--additional-hooks-dir={hooks_dir}",  # 使用自定义hook

        # 🔧 大小优化：使用精确的模块收集而非完整收集
        # 替换--collect-all为更精确的--collect-submodules
        "--collect-submodules=PySide6.QtCore",
        "--collect-submodules=PySide6.QtGui",
        "--collect-submodules=PySide6.QtWidgets",
        "--collect-submodules=PySide6.QtNetwork",
        "--collect-submodules=PySide6.QtSql",
        "--collect-submodules=shiboken6",

        # MNE核心模块（保留电极位置数据和地形图功能）
        "--collect-submodules=mne.io",
        "--collect-submodules=mne.channels",  # 电极位置数据
        "--collect-submodules=mne.viz.topomap",  # 地形图功能
        "--collect-submodules=mne.utils",
        "--collect-submodules=mne.preprocessing",
        "--collect-submodules=mne.filter",

        # 复制必要的元数据
        "--copy-metadata=PySide6",
        "--copy-metadata=shiboken6",
        "--copy-metadata=mne",
        "--copy-metadata=numpy",
        "--copy-metadata=scipy",
        "--copy-metadata=matplotlib",
        "--copy-metadata=pyqtgraph",
        
        # 🔧 数据文件优化：精确包含必要文件，排除开发文件
        "--add-data=assets;assets",
        "--add-data=config;config",  # 包含整个config目录
        "--add-data=data;data",      # 保留用户训练数据
        "--add-data=icons;icons",
        "--add-data=libs;libs",      # 添加libs文件夹，包含电刺激DLL
        "--add-data=ShuJu.db;.",

        # 🔧 排除项目开发文件（通过PyInstaller的内置机制）
        "--exclude-module=docs",     # 排除文档目录
      
        
        # PySide6核心模块
        "--hidden-import=PySide6",
        "--hidden-import=shiboken6",
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtGui",
        "--hidden-import=PySide6.QtWidgets",
        "--hidden-import=PySide6.QtNetwork",
        "--hidden-import=PySide6.QtSql",
        
        # 科学计算库
        "--hidden-import=numpy",
        "--hidden-import=numpy.core",
        "--hidden-import=numpy.core.multiarray",
        "--hidden-import=numpy.lib",
        "--hidden-import=numpy.linalg",
        "--hidden-import=numpy.fft",
        "--hidden-import=numpy.random",
        
        "--hidden-import=scipy",
        "--hidden-import=scipy.sparse",
        "--hidden-import=scipy.sparse.linalg",
        "--hidden-import=scipy.spatial",
        "--hidden-import=scipy.spatial.distance",
        "--hidden-import=scipy.signal",
        "--hidden-import=scipy.stats",
        "--hidden-import=scipy.linalg",
        
        "--hidden-import=pandas",
        "--hidden-import=pandas.core",
        
        # matplotlib
        "--hidden-import=matplotlib",
        "--hidden-import=matplotlib.pyplot",
        "--hidden-import=matplotlib.figure",
        "--hidden-import=matplotlib.backends",
        "--hidden-import=matplotlib.backends.backend_qt5agg",
        "--hidden-import=matplotlib.backends.backend_agg",
        
        # pyqtgraph - 实时曲线显示关键库（精确指定，避免问题模块）
        "--hidden-import=pyqtgraph",
        "--hidden-import=pyqtgraph.graphicsItems",
        "--hidden-import=pyqtgraph.graphicsItems.PlotItem",
        "--hidden-import=pyqtgraph.graphicsItems.ViewBox",
        "--hidden-import=pyqtgraph.graphicsItems.AxisItem",
        "--hidden-import=pyqtgraph.graphicsItems.PlotCurveItem",
        "--hidden-import=pyqtgraph.graphicsItems.PlotDataItem",
        "--hidden-import=pyqtgraph.graphicsItems.GraphicsObject",
        "--hidden-import=pyqtgraph.graphicsItems.GraphicsWidget",
        "--hidden-import=pyqtgraph.widgets",
        "--hidden-import=pyqtgraph.widgets.PlotWidget",
        "--hidden-import=pyqtgraph.widgets.GraphicsLayoutWidget",
        "--hidden-import=pyqtgraph.Qt",
        "--hidden-import=pyqtgraph.Qt.QtCore",
        "--hidden-import=pyqtgraph.Qt.QtGui",
        "--hidden-import=pyqtgraph.Qt.QtWidgets",
        "--hidden-import=pyqtgraph.functions",
        "--hidden-import=pyqtgraph.Point",
        "--hidden-import=pyqtgraph.Vector",
        "--hidden-import=pyqtgraph.Transform3D",
        "--hidden-import=pyqtgraph.SRTTransform3D",
        "--hidden-import=pyqtgraph.debug",
        "--hidden-import=pyqtgraph.reload",
        "--hidden-import=pyqtgraph.colormap",
        "--hidden-import=pyqtgraph.parametertree",

        # 排除可能导致问题的pyqtgraph模块
        "--exclude-module=pyqtgraph.examples",
        "--exclude-module=pyqtgraph.jupyter",
        "--exclude-module=pyqtgraph.opengl",
        
        # MNE脑电信号处理
        "--hidden-import=mne",
        "--hidden-import=mne.io",
        "--hidden-import=mne.utils",
        "--hidden-import=mne.utils._testing",
        "--hidden-import=mne.preprocessing",
        "--hidden-import=mne.viz",
        "--hidden-import=mne.channels",
        "--hidden-import=mne.filter",
        
        # 标准库 - MNE需要
        "--hidden-import=unittest",
        "--hidden-import=unittest.mock",
        "--hidden-import=collections",
        "--hidden-import=collections.abc",
        "--hidden-import=functools",
        "--hidden-import=itertools",
        "--hidden-import=warnings",
        "--hidden-import=inspect",
        "--hidden-import=pydoc",  # PyQtGraph需要pydoc模块
        
        # 其他库
        "--hidden-import=bleak",
        "--hidden-import=bleak.backends",
        "--hidden-import=bleak.backends.winrt",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=cryptography",
        "--hidden-import=cryptography.fernet",
        "--hidden-import=sqlite3",

        # sklearn机器学习库 - 分类器训练必需
        "--hidden-import=sklearn",
        "--hidden-import=sklearn.base",
        "--hidden-import=sklearn.linear_model",
        "--hidden-import=sklearn.svm",
        "--hidden-import=sklearn.ensemble",
        "--hidden-import=sklearn.model_selection",
        "--hidden-import=sklearn.metrics",
        "--hidden-import=sklearn.preprocessing",
        "--hidden-import=sklearn.pipeline",
        "--hidden-import=sklearn.decomposition",
        "--hidden-import=sklearn.discriminant_analysis",

        # 额外的关键科学计算模块
        "--hidden-import=joblib",  # sklearn模型保存
        "--hidden-import=pickle",  # Python序列化
        "--hidden-import=json",    # 配置文件读取
        "--hidden-import=pathlib", # 路径处理

        # setuptools相关依赖 - 修复jaraco.text错误，但排除pkg_resources
        "--hidden-import=setuptools",
        "--exclude-module=setuptools.extern",  # 排除可能有问题的extern模块
        "--hidden-import=jaraco",
        "--hidden-import=jaraco.text",
        "--hidden-import=jaraco.functools",
        "--hidden-import=jaraco.collections",
        "--hidden-import=more_itertools",
        "--copy-metadata=setuptools",
        "--copy-metadata=jaraco.text",
        
        # # 强制包含Python标准库的关键模块
        # "--hidden-import=encodings",
        # "--hidden-import=encodings.utf_8",
        # "--hidden-import=encodings.cp1252",
        # "--hidden-import=codecs",
        
        # 项目模块
        "--hidden-import=app.application",
        "--hidden-import=app.background_loader",
        "--hidden-import=app.config",
        "--hidden-import=core.database",
        "--hidden-import=core.network_config",
        "--hidden-import=core.simple_voice",
        "--hidden-import=core.udp_communicator",
        "--hidden-import=services.auth_service",
        "--hidden-import=services.api_client",
        "--hidden-import=services.patient_service",
        "--hidden-import=services.reference_data_service",
        "--hidden-import=services.treatment_service",
        "--hidden-import=services.user_service",

        # 训练和特征提取核心模块
        "--hidden-import=services.training_session_manager",
        "--hidden-import=services.classifier_training_manager", 
        "--hidden-import=services.weighted_voting_classifier",
        "--hidden-import=services.feature_extraction",
        "--hidden-import=services.feature_extraction.base_extractor",
        "--hidden-import=services.feature_extraction.individual_feature_manager",
        "--hidden-import=services.feature_extraction.config",
        "--hidden-import=services.feature_extraction.fbcsp_extractor",
        "--hidden-import=services.feature_extraction.riemannian_extractor",
        "--hidden-import=services.feature_extraction.tef_extractor",
        "--hidden-import=services.feature_extraction.plv_extractor",
        "--hidden-import=services.feature_extraction.tangent_space_extractor",
        "--hidden-import=services.feature_extraction.utils",
        "--hidden-import=services.feature_extraction.utils.signal_utils",
        "--hidden-import=services.feature_extraction.utils.validation_utils",

        # 电刺激和硬件控制
        "--hidden-import=services.stimulation",
        "--hidden-import=services.stimulation.stimulation_device",
        "--hidden-import=services.bluetooth",
        "--hidden-import=services.bluetooth.standard_bleak_manager",

        # EEG处理模块
        "--hidden-import=services.eeg_processing",
        "--hidden-import=services.eeg_processing.eeg_data_processor",
        "--hidden-import=services.eeg_preprocessing",
        "--hidden-import=services.eeg_preprocessing.kalman_processor",
        "--hidden-import=services.eeg_preprocessing.preprocessing_config",

        "--hidden-import=ui.login_window",
        "--hidden-import=ui.main_window",
        "--hidden-import=ui.components.modern_card",
        "--hidden-import=ui.components.parameter_adjuster",
        "--hidden-import=ui.components.no_wheel_widgets",
        "--hidden-import=ui.components.mne_topography_widget",
        "--hidden-import=ui.components.pyqtgraph_curves_widget",  # 重要：PyQtGraph曲线组件
        "--hidden-import=ui.themes.theme_manager",
        "--hidden-import=utils.db_helpers",
        "--hidden-import=utils.chart_helpers",
        "--hidden-import=utils.user_helpers",
        
        # 🔧 大小优化：排除不必要的模块以减小包大小
        "--exclude-module=tkinter",
        "--exclude-module=doctest",
        "--exclude-module=pydoc_data",
        "--exclude-module=test",
        "--exclude-module=unittest.test",
        "--exclude-module=pip",
        "--exclude-module=wheel",

        # PySide6模块优化
        "--exclude-module=PySide6.QtWebEngineWidgets",
        "--exclude-module=PySide6.QtWebEngineCore",
        "--exclude-module=PySide6.QtWebChannel",
        "--exclude-module=PySide6.QtQuick",
        "--exclude-module=PySide6.QtQml",
        "--exclude-module=PySide6.QtMultimedia",
        "--exclude-module=PySide6.QtOpenGL",
        "--exclude-module=PySide6.scripts",  # 排除PySide6脚本工具
        "--exclude-module=PySide6.tools",   # 排除PySide6工具

        # 科学计算库测试模块
        "--exclude-module=matplotlib.tests",
        "--exclude-module=numpy.tests",
        "--exclude-module=scipy.tests",
        "--exclude-module=sklearn.tests",
        "--exclude-module=pandas.tests",

        # MNE模块优化（保留核心功能，排除不必要部分）
        "--exclude-module=mne.datasets",     # 排除示例数据集
        "--exclude-module=mne.gui",          # 排除GUI工具
        "--exclude-module=mne.commands",     # 排除命令行工具
        "--exclude-module=mne.tests",        # 排除测试文件
        "--exclude-module=mne.viz.backends.renderer",  # 排除3D渲染器
        "--exclude-module=mne.viz._brain",   # 排除脑可视化

        # PyQtGraph优化（已在原脚本中）
        "--exclude-module=pyqtgraph.examples",
        "--exclude-module=pyqtgraph.jupyter",
        "--exclude-module=pyqtgraph.opengl",
        
        "main.py"
    ]
    
    print("执行构建命令...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def ensure_config_files():
    """确保所有配置文件都被正确复制"""
    print("检查并确保配置文件完整...")

    package_config_dir = Path("package_output/脑机接口康复训练系统/_internal/config")
    source_config_dir = Path("config")

    # 确保目标目录存在
    package_config_dir.mkdir(parents=True, exist_ok=True)

    # 关键配置文件列表
    critical_files = [
        "weighted_voting_config.json",
        "classifiers_optimized.json", 
        "feature_extraction_optimized.json",
        "settings.json",
        "users.json"
    ]

    missing_files = []
    for file_name in critical_files:
        source_file = source_config_dir / file_name
        target_file = package_config_dir / file_name

        if source_file.exists():
            try:
                import shutil
                shutil.copy2(source_file, target_file)
                print(f"✓ 复制配置文件: {file_name}")
            except Exception as e:
                print(f"✗ 复制配置文件失败 {file_name}: {e}")
                missing_files.append(file_name)
        else:
            print(f"⚠️ 源配置文件不存在: {file_name}")
            missing_files.append(file_name)

    if missing_files:
        print(f"⚠️ 缺失的配置文件: {missing_files}")
    else:
        print("✓ 所有关键配置文件都已正确复制")


# def post_process():
#     """后处理 - 创建简单启动脚本"""
#     print("创建启动脚本...")

#     package_dir = Path("package_output/脑机接口康复训练系统")

#     # 创建简单启动脚本
#     startup_script = package_dir / "启动系统.bat"
#     with open(startup_script, 'w', encoding='utf-8') as f:
#         f.write("""@echo off
# chcp 65001 >nul
# echo 正在启动脑机接口康复训练系统...
# echo 请稍候...
# "脑机接口康复训练系统.exe"
# if errorlevel 1 (
#     echo.
#     echo 程序异常退出，按任意键关闭...
#     pause >nul
# )
# """)
    



#     # 确保配置文件完整
#     ensure_config_files()

#     print("✓ 后处理完成")


def post_process_size_optimization():
    """后处理 - 大小优化清理"""
    print("执行大小优化后处理...")

    package_dir = Path("package_output/脑机接口康复训练系统/_internal")

    if not package_dir.exists():
        print("⚠️ 打包目录不存在，跳过后处理")
        return

    # 统计优化前大小
    total_size_before = sum(f.stat().st_size for f in package_dir.rglob('*') if f.is_file())

    # 清理可能残留的不必要文件
    cleanup_patterns = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo",
        "**/tests",
        "**/test_*",
        "**/examples",
        "**/demos",
        "**/docs",
        "**/documentation",
        "**/*.md",
        "**/*.rst",
        "**/*.txt",
        "**/LICENSE*",
        "**/COPYING*",
        "**/README*"
    ]

    removed_count = 0
    removed_size = 0

    for pattern in cleanup_patterns:
        for item in package_dir.glob(pattern):
            try:
                if item.is_file():
                    size = item.stat().st_size
                    item.unlink()
                    removed_count += 1
                    removed_size += size
                elif item.is_dir():
                    import shutil
                    size = sum(f.stat().st_size for f in item.rglob('*') if f.is_file())
                    shutil.rmtree(item)
                    removed_count += 1
                    removed_size += size
            except Exception as e:
                print(f"⚠️ 清理文件失败 {item}: {e}")

    # 统计优化后大小
    total_size_after = sum(f.stat().st_size for f in package_dir.rglob('*') if f.is_file())

    print(f"✓ 后处理完成")
    print(f"  清理文件数量: {removed_count}")
    print(f"  清理文件大小: {removed_size / 1024 / 1024:.1f} MB")
    print(f"  优化前大小: {total_size_before / 1024 / 1024:.1f} MB")
    print(f"  优化后大小: {total_size_after / 1024 / 1024:.1f} MB")
    if total_size_before > 0:
        reduction = (total_size_before - total_size_after) / total_size_before * 100
        print(f"  大小减少: {reduction:.1f}%")


def main():
    """主函数 - 大小优化打包流程"""
    print("=" * 60)
    print("脑机接口康复训练系统 - 大小优化打包脚本")
    print("BCI Rehabilitation Training System - Size Optimized Build")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        return False

    # 清理构建
    clean_build()

    # 构建
    if not build_package():
        return False

    # 后处理
    # post_process()
    
    print("=" * 60)
    print("大小优化打包完成！")
    print("=" * 60)
    print("打包文件位置：package_output/脑机接口康复训练系统/")
    print("主程序：脑机接口康复训练系统.exe")
    print()
    print("大小优化特性：")
    print("✓ 字节码优化 (-O)")
    print("✓ 无控制台窗口 (--windowed)")
    print("✓ 精确模块收集（替代完整收集）")
    print("✓ 排除第三方库示例和测试文件")
    print("✓ 排除MNE数据集和GUI工具")
    print("✓ 排除PySide6脚本和工具")
    print("✓ 排除项目文档目录")
    print("✓ 自定义优化Hook文件")
    print()
    print("保留的核心功能：")
    print("✓ PyQtGraph实时曲线显示")
    print("✓ MNE脑电地形图显示")
    print("✓ MNE电极位置数据")
    print("✓ 用户训练数据（data目录）")
    print("✓ 所有核心业务功能")
    print()
    print("预期包大小减少：30-50%")
    print("可以将整个文件夹复制到其他计算机运行")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        input("优化打包失败，按回车键退出...")
        sys.exit(1)
    else:
        input("优化打包完成，按回车键退出...")