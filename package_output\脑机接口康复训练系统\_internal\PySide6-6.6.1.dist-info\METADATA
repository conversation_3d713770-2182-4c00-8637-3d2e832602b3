Metadata-Version: 2.1
Name: PySide6
Version: 6.6.1
Summary: Python bindings for the Qt cross-platform application and UI framework
Author-email: Qt for Python Team <<EMAIL>>
License: LGPL
Project-URL: Homepage, https://pyside.org
Project-URL: Documentation, https://doc.qt.io/qtforpython
Project-URL: Repository, https://code.qt.io/cgit/pyside/pyside-setup.git/
Project-URL: Changelog, https://code.qt.io/cgit/pyside/pyside-setup.git/tree/doc/changelogs
Project-URL: Tracker, https://bugreports.qt.io/projects/PYSIDE
Keywords: Qt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Environment :: X11 Applications :: Qt
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)
Classifier: License :: Other/Proprietary License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: Software Development :: Widget Sets
Requires-Python: <3.13,>=3.8
Description-Content-Type: text/markdown
License-File: LicenseRef-Qt-Commercial.txt
Requires-Dist: shiboken6 (==6.6.1)
Requires-Dist: PySide6-Essentials (==6.6.1)
Requires-Dist: PySide6-Addons (==6.6.1)

# PySide6

### Introduction

**Important:** for Qt5 compatibility, check [PySide2](https://pypi.org/project/PySide2)

PySide6 is the official Python module from the
[Qt for Python project](https://wiki.qt.io/Qt_for_Python),
which provides access to the complete Qt 6.0+ framework.

The Qt for Python project is developed in the open, with all facilities you'd expect
from any modern OSS project such as all code in a git repository and an open
design process. We welcome any contribution conforming to the
[Qt Contribution Agreement](https://www.qt.io/contributionagreement/).

### Installation

Since the release of the [Technical Preview](https://blog.qt.io/blog/2018/06/13/qt-python-5-11-released/)
it is possible to install via `pip`, both from Qt's servers
and [PyPi](https://pypi.org/project/PySide6/):

```
pip install PySide6
```

> Please note: this wheel is an alias to other two wheels
> [PySide6_Essentials](https://pypi.org/project/PySide6_Essentials) and
> [PySide6_Addons](https://pypi.org/project/PySide6_Addons), which contains
> a predefined list of Qt Modules.

#### Dependencies

PySide6 versions following 6.0 use a C++ parser based on
[Clang](http://clang.org/). The Clang library (C-bindings), version 13.0 or
higher is required for building. Prebuilt versions of it can be downloaded from
[download.qt.io](https://download.qt.io/development_releases/prebuilt/libclang/).

After unpacking the archive, set the environment variable *LLVM_INSTALL_DIR* to
point to the folder containing the *include* and *lib* directories of Clang:

```
7z x .../libclang-release_100-linux-Rhel7.2-gcc5.3-x86_64-clazy.7z
export LLVM_INSTALL_DIR=$PWD/libclang
```

On Windows:

```
7z x .../libclang-release_100-windows-vs2015_64-clazy.7z
SET LLVM_INSTALL_DIR=%CD%\libclang
```

### Building from source

For building PySide6 from scratch, please read about
[getting started](https://doc.qt.io/qtforpython/gettingstarted.html).
This process will include getting the code:

```
git clone https://code.qt.io/pyside/pyside-setup
cd pyside-setup
git checkout 6.x # if a specific version is needed
```

then install the dependencies, and following the instructions per platform.
A common build command will look like:

```
python setup.py install --qtpaths=/path/to/bin/qtpaths6 --parallel=8 --build-tests
```

You can obtain more information about the options to build PySide and Shiboken
in [our wiki](https://wiki.qt.io/Qt_for_Python/).

### Documentation and Bugs

You can find more information about the PySide6 module API in the
[official Qt for Python documentation](https://doc.qt.io/qtforpython/).

If you come across any issue, please file a bug report at our
[JIRA tracker](https://bugreports.qt.io/projects/PYSIDE) following
our [guidelines](https://wiki.qt.io/Qt_for_Python/Reporting_Bugs).

### Community

Check our channels on IRC (Libera), Telegram, Gitter, Matrix, and mailing list,
and [join our community](https://wiki.qt.io/Qt_for_Python#Community)!

### Licensing

PySide6 is available under both Open Source (LGPLv3/GPLv2) and commercial
license. Using PyPi is the recommended installation source, because the
content of the wheels is valid for both cases. For more information, refer to
the [Qt Licensing page](https://www.qt.io/licensing/).
