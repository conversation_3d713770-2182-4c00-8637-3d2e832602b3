#pragma once

#include <windows.h>
//循环刺激参数;
#ifndef CYCLEACTPARANEW_H_H
#define CYCLEACTPARANEW_H_H
typedef struct  _CYCLEACTPARA
{
	int ChanNum;
	double ActFreq;
	double PulseWidth;
	double RelaxTime;
	double ClimbTime;
	double WorkTime;
	double FallTime;
	int WaveType;
}  CYCLEACTPARA ,*LPCYCLEACTPARA;
#endif

#ifndef _DLLTUT_DLL_H_
#define _DLLTUT_DLL_H_
#if defined DLL_EXPORT
#define DECLDIR __declspec(dllexport)
#else
#define DECLDIR __declspec(dllimport)
#endif

// 该回调函数返回-1时，停止读取数据发送数据;
typedef int (__stdcall *funDataProc)(HANDLE pHandle ,short* lpBuffer, int nSize);

extern "C" __declspec(dllexport) int  CloseRecPort();           //关闭设备端口;
extern "C" __declspec(dllexport) int  OpenRecPort(int portNum,int iReadSize,funDataProc fun,HANDLE pHandle);

//extern "C"告诉编译器该部分可以在C/C++中使用;

extern "C" __declspec(dllexport) BOOL IsRecOpen();             //判断是否打开;

/*------------------------------------------
函数功能：读取设备信息命令函数;
参数：输入数组长度为20，用于存放20字节的设备信息
返回值：0：命令执行正确
        -1：命令执行错误 
-------------------------------------------*/
extern "C" __declspec(dllexport) int ReadDeviceInfo(char* DevInfo); 

/*_____________________________________________________________________ 
切换设备工作状态命令函数;
参数：nStateNum,设备状态编码,0：空闲状态,1：循环刺激;
返回值：0：命令执行正确；
        1：命令错误，包括收到不执行的命令码、通道号等；
        2：命令参数错误，收到的命令中参数超限；
        3：校验错误；
        4：没有读取到硬件的命令应答；
        5：将命令写入串口时失败；
_____________________________________________________________________*/

extern "C" __declspec(dllexport) int SwitchDeviceState(int nStateNum);
/*_____________________________________________________________________
 切换通道工作状态命令函数;
 参数：nStateNum,为通道工作状态编码，0：停止，1：暂停，2：电流调节，
 3：正常工作；
 参数：nChanNum为目标通道的号码;
 返回值：同上
_____________________________________________________________________*/
extern "C" __declspec(dllexport) int SwitchChannelState(int nChanNum,int nStateNum);

/*_____________________________________________________________________
设置循环刺激参数命令函数;
参数：lpCycleActPara:一个指向CYCLEACTPARA结构体的常量引用，具体参数见协议;
返回值：同上
_____________________________________________________________________*/

extern "C" __declspec(dllexport) int StimParaSet(CYCLEACTPARA& CycleActParaNew);

/*_____________________________________________________________________
电流调节命令函数;
参数：ChanNum为目标通道的号码;
参数：bClimb步长调节，true：下调一个步长，false：上调一个步长；
参数：nStep电流调节步长，0：0.1mA，1：0.2mA，2：0.5mA，3：1.0mA，4：2.0mA，5：5.0mA;
返回值：同上
_____________________________________________________________________*/
extern "C" __declspec(dllexport) int RegulateCurrent( int nChanNum, int nStep, bool bClimb);
/*_____________________________________________________________________
// 电流强度偏移量调节命令函数;
参数：nChanNum为目标通道的号码,0x01: 通道1，0x02: 通道2，0x3: 关节角度握力采集通道;
参数：bCurrentRaise为布尔型，true：下调一个步长，false:上调一个步长;
返回值：同上
_____________________________________________________________________*/
extern "C" __declspec(dllexport) int RegulateCurrentOffet( int nChanNum, bool bCurrentRaise);
/*_____________________________________________________________________
电流强度设定命令函数;
参数：nChanNum为目标通道的号码,nValue电流强度设定值;
返回值：同上
_____________________________________________________________________*/
extern "C" __declspec(dllexport) int CurrentSet( int nChanNum, int nValue);


/*_____________________________________________________________________
读取串口数据函数;
参数：byBuffer：存储数据的缓存；nReadOnceDataNum：要去串口读取数据的大小;
返回值：实际从串口读取到数据的大小;
_____________________________________________________________________*/
extern "C" __declspec(dllexport) DWORD  ReadData(BYTE *byBuffer,int nReadOnceDataNum);


//循环刺激参数设置命令;此函数功能同StimParaSet，如使用结构体不方便时，可调用次函数
extern "C" __declspec(dllexport) int StimPara( int ChanNum,        //通道号;
											    double ActFreq,    //频率;
												double PulseWidth, //脉宽;
												double RelaxTime,  //休息时间;
												double ClimbTime,  //上升时间;
												double WorkTime,   //工作时间;
												double FallTime,   //下降时间;
												int WaveType);     //循环刺激参数;


#endif
